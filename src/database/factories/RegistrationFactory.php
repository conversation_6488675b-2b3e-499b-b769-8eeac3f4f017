<?php

namespace Database\Factories;

use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationType;
use App\Enums\RegistrationStatus;
use App\Enums\PaymentStatus;
use App\Models\Registration;
use App\Models\User;
use App\Models\Child;
use App\Models\Camp;
use App\Models\Training;
use Illuminate\Database\Eloquent\Factories\Factory;

class RegistrationFactory extends Factory
{
    protected $model = Registration::class;

    public function definition(): array
    {
        $user = User::factory()->create(['status' => 'active']);
        $child = Child::factory()->create(['guardian_user_id' => $user->id]);
        $camp = Camp::factory()->create();

        return [
            'user_id' => $user->id,
            'child_id' => $child->id,
            'type' => RegistrationSourceType::CAMP,
            'source_id' => $camp->id,
            'registration_type' => RegistrationType::CHILD_REGISTRATION,
            'status' => RegistrationStatus::APPROVED,
            'payment_status' => PaymentStatus::PAID,
            'registered_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'total_amount' => $camp->price + $this->faker->numberBetween(-50, 100),
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ];
    }

    public function forCamp(Camp $camp): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => RegistrationSourceType::CAMP,
            'source_id' => $camp->id,
            'registration_type' => RegistrationType::CHILD_REGISTRATION,
        ]);
    }

    public function forTraining(Training $training): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => RegistrationSourceType::TRAINING,
            'source_id' => $training->id,
            'registration_type' => RegistrationType::TRAINEE_REGISTRATION,
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RegistrationStatus::PENDING_APPROVAL,
        ]);
    }

    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RegistrationStatus::APPROVED,
        ]);
    }

    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RegistrationStatus::REJECTED,
        ]);
    }

    public function waitingList(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RegistrationStatus::WAITING_LIST,
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => RegistrationStatus::CANCELLED,
        ]);
    }

    public function unpaid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => PaymentStatus::UNPAID,
        ]);
    }

    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => PaymentStatus::PAID,
        ]);
    }

    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => PaymentStatus::REFUNDED,
        ]);
    }
} 