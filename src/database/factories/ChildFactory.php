<?php

namespace Database\Factories;

use App\Models\Child;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ChildFactory extends Factory
{
    protected $model = Child::class;

    public function definition(): array
    {
        $allergies = [
            'Peanuts',
            'Tree nuts',
            'Dairy',
            'Eggs',
            'Soy',
            'Wheat',
            'Fish',
            'Shellfish',
            'Latex',
            null,
        ];

        $medicalConditions = [
            'Asthma',
            'Diabetes',
            'Epilepsy',
            'ADHD',
            'Autism',
            'Heart condition',
            'Food allergies',
            null,
        ];

        $guardian = User::factory()->create(['status' => 'active']);

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'date_of_birth' => $this->faker->dateTimeBetween('-15 years', '-5 years'),
            'allergies' => $this->faker->randomElement($allergies),
            'medical_conditions' => $this->faker->randomElement($medicalConditions),
            'emergency_contact_name' => $guardian->first_name . ' ' . $guardian->last_name,
            'emergency_contact_phone' => $guardian->phone_number,
            'guardian_user_id' => $guardian->id,
        ];
    }

    public function withAllergies(): static
    {
        return $this->state(fn (array $attributes) => [
            'allergies' => $this->faker->randomElement(['Peanuts', 'Tree nuts', 'Dairy', 'Eggs', 'Soy']),
        ]);
    }

    public function withMedicalConditions(): static
    {
        return $this->state(fn (array $attributes) => [
            'medical_conditions' => $this->faker->randomElement(['Asthma', 'Diabetes', 'Epilepsy', 'ADHD', 'Autism']),
        ]);
    }

    public function healthy(): static
    {
        return $this->state(fn (array $attributes) => [
            'allergies' => null,
            'medical_conditions' => null,
        ]);
    }
} 