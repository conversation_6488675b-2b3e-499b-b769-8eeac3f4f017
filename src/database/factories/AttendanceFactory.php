<?php

namespace Database\Factories;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Registration;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttendanceFactory extends Factory
{
    protected $model = Attendance::class;

    public function definition(): array
    {
        $registration = Registration::factory()->approved()->create();

        return [
            'registration_id' => $registration->id,
            'attendance_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'status_id' => AttendanceStatus::PRESENT,
            'notes' => null,
        ];
    }

    public function present(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_id' => AttendanceStatus::PRESENT,
            'notes' => $this->faker->randomElement(['On time', 'Participated well', 'Good behavior', null]),
        ]);
    }

    public function absent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_id' => AttendanceStatus::ABSENT,
            'notes' => $this->faker->randomElement(['Sick', 'Family vacation', 'Personal reasons', 'No reason provided']),
        ]);
    }

    public function late(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_id' => AttendanceStatus::LATE,
            'notes' => $this->faker->randomElement(['Arrived 15 minutes late', 'Traffic delay', 'Family emergency', 'Overslept']),
        ]);
    }

    public function excused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status_id' => AttendanceStatus::EXCUSED,
            'notes' => $this->faker->randomElement(['Doctor appointment', 'Family emergency', 'School event', 'Religious holiday']),
        ]);
    }

    public function forDate(string $date): static
    {
        return $this->state(fn (array $attributes) => [
            'attendance_date' => $date,
        ]);
    }

    public function forRegistration(Registration $registration): static
    {
        return $this->state(fn (array $attributes) => [
            'registration_id' => $registration->id,
        ]);
    }
} 