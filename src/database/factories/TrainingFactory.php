<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class TrainingFactory extends Factory
{
    protected $model = Training::class;

    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('now', '+2 months');
        $endDate = $this->faker->dateTimeBetween($startDate, '+3 months');

        return [
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'location' => $this->faker->city(),
            'price' => $this->faker->numberBetween(50, 500),
            'status' => $this->faker->randomElement(TrainingConst::getStatuses()),
            'max_capacity' => $this->faker->numberBetween(10, 100),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TrainingConst::STATUS_ACTIVE,
        ]);
    }

    public function full(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TrainingConst::STATUS_FULL,
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TrainingConst::STATUS_CANCELLED,
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TrainingConst::STATUS_COMPLETED,
        ]);
    }

    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TrainingConst::STATUS_ARCHIVED,
        ]);
    }

    public function withUser(?User $user = null): static
    {
        $user = $user ?: User::factory()->create();

        return $this->state(fn (array $attributes) => [
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);
    }
}
