<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the attendance_pdf_slips table to store individual PDF attendance slips.
     * Each PDF is generated for ONE person's attendance record and contains:
     * - Name, Age (from DOB), Parent/Guardian, Health Notes
     * 
     * This table links to the attendances table (one PDF per attendance record).
     * The attendances table already has validation fields to lock records after validation.
     */
    public function up(): void
    {
        Schema::create('attendance_pdf_slips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attendance_id')->constrained('attendances', 'attendance_id')->onDelete('cascade');
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_size')->nullable(); // File size in bytes
            $table->string('mime_type')->default('application/pdf');
            $table->uuid('generated_by');
            $table->timestamp('generated_at');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('generated_by')->references('id')->on('users')->onDelete('cascade');
            
            // Indexes for better query performance
            $table->index('attendance_id', 'attendance_pdf_slips_attendance_index');
            $table->index('generated_by', 'attendance_pdf_slips_generated_by_index');
            $table->index('generated_at', 'attendance_pdf_slips_generated_at_index');
            $table->index('file_name', 'attendance_pdf_slips_file_name_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_pdf_slips');
    }
};
