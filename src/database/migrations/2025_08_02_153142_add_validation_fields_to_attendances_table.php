<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table->boolean('is_validated')->default(false)->after('notes');
            $table->foreignUuid('validated_by')->nullable()->constrained('users')->nullOnDelete()->after('is_validated');
            $table->timestamp('validated_at')->nullable()->after('validated_by');
            
            $table->index(['is_validated', 'attendance_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table->dropForeign(['validated_by']);
            $table->dropIndex(['is_validated', 'attendance_date']);
            $table->dropColumn(['is_validated', 'validated_by', 'validated_at']);
        });
    }
};
