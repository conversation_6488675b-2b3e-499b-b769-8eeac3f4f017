<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('training_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('training_id')->constrained()->onDelete('cascade');
            $table->date('attendance_date');
            $table->boolean('is_validated')->default(false);
            $table->uuid('validated_by')->nullable();
            $table->timestamp('validated_at')->nullable();
            $table->timestamps();

            $table->foreign('validated_by')->references('id')->on('users')->onDelete('set null');
            $table->unique(['training_id', 'attendance_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('training_attendances');
    }
}; 