<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trainings', function (Blueprint $table) {
            $table->index('name', 'trainings_name_index');
            $table->index('location', 'trainings_location_index');
            $table->index('start_date', 'trainings_start_date_index');
            $table->index('end_date', 'trainings_end_date_index');
            $table->index('price', 'trainings_price_index');
            $table->index('status', 'trainings_status_index');
            // Composite index
            $table->index(['status', 'start_date'], 'trainings_status_start_date_index');
            $table->index(['status', 'price'], 'trainings_status_price_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trainings', function (Blueprint $table) {
            $table->dropIndex('trainings_name_index');
            $table->dropIndex('trainings_location_index');
            $table->dropIndex('trainings_start_date_index');
            $table->dropIndex('trainings_end_date_index');
            $table->dropIndex('trainings_price_index');
            $table->dropIndex('trainings_status_index');
            $table->dropIndex('trainings_status_start_date_index');
            $table->dropIndex('trainings_status_price_index');
        });
    }
};
