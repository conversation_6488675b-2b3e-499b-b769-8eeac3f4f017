<?php

use App\Enums\AttendanceStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('attendances', function (Blueprint $table) {
            $table->id('attendance_id');
            $table->foreignId('registration_id')->constrained('registrations')->cascadeOnDelete();
            $table->dateTime('attendance_date');
            $table->enum('status_id', AttendanceStatus::values());
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->unique(['registration_id', 'attendance_date']);
            $table->index(['attendance_date', 'status_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('attendances');
    }
}; 