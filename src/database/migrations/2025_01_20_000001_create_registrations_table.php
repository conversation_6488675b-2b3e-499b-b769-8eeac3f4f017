<?php

use App\Enums\PaymentStatus;
use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationStatus;
use App\Enums\RegistrationType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('user_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('child_id')->nullable()->constrained('children')->nullOnDelete();
            $table->enum('type', RegistrationSourceType::values());
            $table->unsignedBigInteger('source_id');
            $table->enum('registration_type', RegistrationType::values());
            $table->enum('status', RegistrationStatus::values());
            $table->enum('payment_status', PaymentStatus::values());
            $table->dateTime('registered_at');
            $table->decimal('total_amount', 10, 2);
            $table->foreignUuid('created_by')->constrained('users')->cascadeOnDelete();
            $table->foreignUuid('updated_by')->constrained('users')->cascadeOnDelete();
            $table->timestamps();

            $table->index(['type', 'source_id']);
            $table->index(['status', 'payment_status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('registrations');
    }
};
