<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('training_attendance_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('training_attendance_id')->constrained()->onDelete('cascade');
            $table->foreignId('training_participant_id')->constrained()->onDelete('cascade');
            $table->boolean('is_present')->default(false);
            $table->timestamps();

            $table->unique(['training_attendance_id', 'training_participant_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('training_attendance_records');
    }
}; 