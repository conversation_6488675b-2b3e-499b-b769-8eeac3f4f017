<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Adds additional fields to the existing files table to support attendance PDF generation.
     * The files table will store PDF attendance slips using polymorphic relationships:
     * - fileable_type = 'App\Models\Attendance'
     * - fileable_id = attendance_id
     * - type = 'attendance_pdf'
     *
     * Each PDF is generated for ONE person's attendance record and contains:
     * - Name, Age (from DOB), Parent/Guardian, Health Notes
     */
    public function up(): void
    {
        Schema::table('files', function (Blueprint $table) {
            // Add generated_by field to track who created the file
            $table->uuid('generated_by')->nullable()->after('mime_type');
            $table->timestamp('generated_at')->nullable()->after('generated_by');

            // Foreign key constraint
            $table->foreign('generated_by')->references('id')->on('users')->onDelete('set null');

            // Add indexes for better performance
            $table->index('generated_by', 'files_generated_by_index');
            $table->index('generated_at', 'files_generated_at_index');
            $table->index('type', 'files_type_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('files', function (Blueprint $table) {
            $table->dropForeign(['generated_by']);
            $table->dropIndex('files_generated_by_index');
            $table->dropIndex('files_generated_at_index');
            $table->dropIndex('files_type_index');
            $table->dropColumn(['generated_by', 'generated_at']);
        });
    }
};
