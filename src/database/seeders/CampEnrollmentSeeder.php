<?php

namespace Database\Seeders;

use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationType;
use App\Enums\RegistrationStatus;
use App\Enums\PaymentStatus;
use App\Enums\AttendanceStatus;
use App\Models\Camp;
use App\Models\Child;
use App\Models\Registration;
use App\Models\Attendance;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class CampEnrollmentSeeder extends Seeder
{
    public function run(): void
    {
        $camps = Camp::all();
        $parents = User::where('status', 'active')->take(10)->get();

        // Create children and registrations
        foreach ($camps as $camp) {
            $campParents = $parents->random(rand(3, 6));
            
            foreach ($campParents as $parent) {
                // Create 1-3 children per parent
                $childrenCount = rand(1, 3);
                
                for ($i = 0; $i < $childrenCount; $i++) {
                    $child = Child::create([
                        'first_name' => fake()->firstName(),
                        'last_name' => fake()->lastName(),
                        'date_of_birth' => fake()->dateTimeBetween('-15 years', '-5 years'),
                        'allergies' => $this->getRandomAllergies(),
                        'medical_conditions' => $this->getRandomMedicalConditions(),
                                            'emergency_contact_name' => $parent->first_name . ' ' . $parent->last_name,
                    'emergency_contact_phone' => $parent->phone_number ?: '**********',
                        'guardian_user_id' => $parent->id,
                    ]);

                    $registration = Registration::create([
                        'user_id' => $parent->id,
                        'child_id' => $child->id,
                        'type' => RegistrationSourceType::CAMP,
                        'source_id' => $camp->id,
                        'registration_type' => RegistrationType::CHILD_REGISTRATION,
                        'status' => $this->getRandomStatus(),
                        'payment_status' => $this->getRandomPaymentStatus(),
                        'registered_at' => fake()->dateTimeBetween('-30 days', 'now'),
                        'total_amount' => $camp->price + rand(-50, 100),
                        'created_by' => $parent->id,
                        'updated_by' => $parent->id,
                    ]);

                    // Create attendance records for approved registrations
                    if ($registration->status === RegistrationStatus::APPROVED) {
                        $this->createAttendanceRecords($registration, $camp);
                    }
                }
            }
        }

        // Create some training registrations
        $this->createTrainingRegistrations($parents);
    }

    private function getRandomAllergies(): ?string
    {
        $allergies = [
            'Peanuts',
            'Tree nuts',
            'Dairy',
            'Eggs',
            'Soy',
            'Wheat',
            'Fish',
            'Shellfish',
            'Latex',
            null,
            null,
        ];
        
        return fake()->randomElement($allergies);
    }

    private function getRandomMedicalConditions(): ?string
    {
        $conditions = [
            'Asthma',
            'Diabetes',
            'Epilepsy',
            'ADHD',
            'Autism',
            'Heart condition',
            'Food allergies',
            null,
            null,
            null,
        ];
        
        return fake()->randomElement($conditions);
    }

    private function getRandomStatus(): RegistrationStatus
    {
        $statuses = [
            RegistrationStatus::PENDING_APPROVAL,
            RegistrationStatus::APPROVED,
            RegistrationStatus::APPROVED,
            RegistrationStatus::APPROVED,
            RegistrationStatus::WAITING_LIST,
            RegistrationStatus::REJECTED,
        ];
        
        return fake()->randomElement($statuses);
    }

    private function getRandomPaymentStatus(): PaymentStatus
    {
        $statuses = [
            PaymentStatus::UNPAID,
            PaymentStatus::PAID,
            PaymentStatus::PAID,
            PaymentStatus::PAID,
            PaymentStatus::REFUNDED,
        ];
        
        return fake()->randomElement($statuses);
    }

    private function createAttendanceRecords(Registration $registration, Camp $camp): void
    {
        $startDate = Carbon::parse($camp->start_date);
        $endDate = Carbon::parse($camp->end_date);
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate && $currentDate <= now()) {
            // Skip weekends (optional)
            if ($currentDate->isWeekend() && rand(0, 1)) {
                $currentDate->addDay();
                continue;
            }

            $attendanceStatus = $this->getRandomAttendanceStatus();
            
            Attendance::create([
                'registration_id' => $registration->id,
                'attendance_date' => $currentDate->format('Y-m-d'),
                'status_id' => $attendanceStatus,
                'notes' => $this->getAttendanceNotes($attendanceStatus),
            ]);

            $currentDate->addDay();
        }
    }

    private function getRandomAttendanceStatus(): AttendanceStatus
    {
        $statuses = [
            AttendanceStatus::PRESENT,
            AttendanceStatus::PRESENT,
            AttendanceStatus::PRESENT,
            AttendanceStatus::PRESENT,
            AttendanceStatus::PRESENT,
            AttendanceStatus::LATE,
            AttendanceStatus::ABSENT,
            AttendanceStatus::EXCUSED,
        ];
        
        return fake()->randomElement($statuses);
    }

    private function getAttendanceNotes(AttendanceStatus $status): ?string
    {
        $notes = [
            'present' => [
                'On time',
                'Participated well',
                'Good behavior',
                null,
                null,
            ],
            'late' => [
                'Arrived 15 minutes late',
                'Traffic delay',
                'Family emergency',
                'Overslept',
            ],
            'absent' => [
                'Sick',
                'Family vacation',
                'Personal reasons',
                'No reason provided',
            ],
            'excused' => [
                'Doctor appointment',
                'Family emergency',
                'School event',
                'Religious holiday',
            ],
        ];

        return fake()->randomElement($notes[$status->value] ?? [null]);
    }

    private function createTrainingRegistrations($parents): void
    {
        // Create some training registrations for variety
        $trainings = \App\Models\Training::all();
        
        foreach ($trainings as $training) {
            $trainingParents = $parents->random(rand(2, 4));
            
            foreach ($trainingParents as $parent) {
                $child = Child::create([
                    'first_name' => fake()->firstName(),
                    'last_name' => fake()->lastName(),
                    'date_of_birth' => fake()->dateTimeBetween('-18 years', '-12 years'),
                    'allergies' => $this->getRandomAllergies(),
                    'medical_conditions' => $this->getRandomMedicalConditions(),
                    'emergency_contact_name' => $parent->first_name . ' ' . $parent->last_name,
                    'emergency_contact_phone' => $parent->phone_number ?: '**********',
                    'guardian_user_id' => $parent->id,
                ]);

                Registration::create([
                    'user_id' => $parent->id,
                    'child_id' => $child->id,
                    'type' => RegistrationSourceType::TRAINING,
                    'source_id' => $training->id,
                    'registration_type' => RegistrationType::TRAINEE_REGISTRATION,
                    'status' => $this->getRandomStatus(),
                    'payment_status' => $this->getRandomPaymentStatus(),
                    'registered_at' => fake()->dateTimeBetween('-30 days', 'now'),
                    'total_amount' => rand(100, 500),
                    'created_by' => $parent->id,
                    'updated_by' => $parent->id,
                ]);
            }
        }
    }
} 