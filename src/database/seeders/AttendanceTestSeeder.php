<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Const\Training as TrainingConst;
use App\Enums\AttendanceStatus;
use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationStatus;
use App\Models\Attendance;
use App\Models\Child;
use App\Models\Registration;
use App\Models\Training;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AttendanceTestSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        $admin = User::factory()->create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone_number' => '1234567890',
            'status' => 'active',
        ]);

        // Create participant users
        $participants = User::factory()->count(5)->create([
            'status' => 'active',
        ]);

        // Create children for participants
        $children = [];
        foreach ($participants as $participant) {
            $children[] = Child::factory()->create([
                'guardian_user_id' => $participant->id,
            ]);
        }

        // Create active training
        $activeTraining = Training::factory()->create([
            'name' => 'Swimming Training - Active',
            'description' => 'Active swimming training for testing attendance',
            'start_date' => Carbon::today()->subDays(5),
            'end_date' => Carbon::today()->addDays(10),
            'location' => 'Community Pool',
            'price' => 150.00,
            'status' => TrainingConst::STATUS_ACTIVE,
            'max_capacity' => 20,
            'created_by' => $admin->id,
            'updated_by' => $admin->id,
        ]);

        // Create completed training
        $completedTraining = Training::factory()->create([
            'name' => 'Swimming Training - Completed',
            'description' => 'Completed swimming training for testing access control',
            'start_date' => Carbon::today()->subDays(20),
            'end_date' => Carbon::today()->subDays(5),
            'location' => 'Community Pool',
            'price' => 150.00,
            'status' => TrainingConst::STATUS_COMPLETED,
            'max_capacity' => 20,
            'created_by' => $admin->id,
            'updated_by' => $admin->id,
        ]);

        // Create cancelled training
        $cancelledTraining = Training::factory()->create([
            'name' => 'Swimming Training - Cancelled',
            'description' => 'Cancelled swimming training for testing access control',
            'start_date' => Carbon::today()->subDays(5),
            'end_date' => Carbon::today()->addDays(10),
            'location' => 'Community Pool',
            'price' => 150.00,
            'status' => TrainingConst::STATUS_CANCELLED,
            'max_capacity' => 20,
            'created_by' => $admin->id,
            'updated_by' => $admin->id,
        ]);

        // Create registrations for active training
        $activeRegistrations = [];
        for ($i = 0; $i < 3; $i++) {
            $activeRegistrations[] = Registration::factory()->create([
                'user_id' => $participants[$i]->id,
                'child_id' => $children[$i]->id,
                'type' => RegistrationSourceType::TRAINING,
                'source_id' => $activeTraining->id,
                'registration_type' => \App\Enums\RegistrationType::TRAINEE_REGISTRATION,
                'status' => RegistrationStatus::APPROVED,
                'payment_status' => \App\Enums\PaymentStatus::PAID,
                'registered_at' => Carbon::now()->subDays(10),
                'total_amount' => 150.00,
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ]);
        }

        // Create registrations for completed training
        for ($i = 0; $i < 2; $i++) {
            Registration::factory()->create([
                'user_id' => $participants[$i + 3]->id,
                'child_id' => $children[$i + 3]->id,
                'type' => RegistrationSourceType::TRAINING,
                'source_id' => $completedTraining->id,
                'registration_type' => \App\Enums\RegistrationType::TRAINEE_REGISTRATION,
                'status' => RegistrationStatus::APPROVED,
                'payment_status' => \App\Enums\PaymentStatus::PAID,
                'registered_at' => Carbon::now()->subDays(25),
                'total_amount' => 150.00,
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ]);
        }

        // Create some existing attendance records for active training
        $today = Carbon::today();
        $yesterday = Carbon::today()->subDay();

        // Yesterday's attendance (not validated)
        foreach ($activeRegistrations as $registration) {
            Attendance::factory()->create([
                'registration_id' => $registration->id,
                'attendance_date' => $yesterday,
                'status_id' => AttendanceStatus::PRESENT,
                'notes' => 'On time',
                'is_validated' => false,
            ]);
        }

        // Today's attendance (partially filled, not validated)
        Attendance::factory()->create([
            'registration_id' => $activeRegistrations[0]->id,
            'attendance_date' => $today,
            'status_id' => AttendanceStatus::PRESENT,
            'notes' => 'On time',
            'is_validated' => false,
        ]);

        // Create a validated attendance record for testing access control
        $validatedDate = Carbon::today()->subDays(2);
        foreach ($activeRegistrations as $registration) {
            Attendance::factory()->create([
                'registration_id' => $registration->id,
                'attendance_date' => $validatedDate,
                'status_id' => AttendanceStatus::PRESENT,
                'notes' => 'Validated session',
                'is_validated' => true,
                'validated_by' => $admin->id,
                'validated_at' => $validatedDate->setTime(10, 30),
            ]);
        }

        // Create some pending registrations (should not appear in attendance)
        Registration::factory()->create([
            'user_id' => $participants[4]->id,
            'child_id' => $children[4]->id,
            'type' => RegistrationSourceType::TRAINING,
            'source_id' => $activeTraining->id,
            'registration_type' => \App\Enums\RegistrationType::TRAINEE_REGISTRATION,
            'status' => RegistrationStatus::WAITING_LIST,
            'payment_status' => \App\Enums\PaymentStatus::UNPAID,
            'registered_at' => Carbon::now()->subDays(5),
            'total_amount' => 150.00,
            'created_by' => $admin->id,
            'updated_by' => $admin->id,
        ]);

        $this->command->info('Attendance test data created successfully!');
        $this->command->info('Admin user: <EMAIL>');
        $this->command->info('Active Training ID: ' . $activeTraining->id);
        $this->command->info('Completed Training ID: ' . $completedTraining->id);
        $this->command->info('Cancelled Training ID: ' . $cancelledTraining->id);
        $this->command->info('');
        $this->command->info('Test scenarios available:');
        $this->command->info('- Active training with 3 participants');
        $this->command->info('- Yesterday: All participants marked (not validated)');
        $this->command->info('- Today: 1 participant marked (incomplete)');
        $this->command->info('- 2 days ago: All participants marked and validated');
        $this->command->info('- 1 pending registration (should not appear in attendance)');
    }
}
