<?php

namespace Database\Seeders;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use Illuminate\Database\Seeder;

class TrainingSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        
        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        $adminUser = $users->first();

        $trainings = [
            [
                'name' => 'Advanced Laravel Development',
                'description' => 'Learn advanced Laravel concepts including queues, events, and testing.',
                'start_date' => now()->addDays(30),
                'end_date' => now()->addDays(32),
                'location' => 'Online',
                'price' => 299.99,
                'status' => TrainingConst::STATUS_ACTIVE,
                'max_capacity' => 50,
                'created_by' => $adminUser->id,
                'updated_by' => $adminUser->id,
            ],
            [
                'name' => 'API Development Best Practices',
                'description' => 'Master RESTful API design and implementation.',
                'start_date' => now()->addDays(45),
                'end_date' => now()->addDays(47),
                'location' => 'Conference Center A',
                'price' => 199.99,
                'status' => TrainingConst::STATUS_ACTIVE,
                'max_capacity' => 30,
                'created_by' => $adminUser->id,
                'updated_by' => $adminUser->id,
            ],
            [
                'name' => 'Database Optimization',
                'description' => 'Learn database design and optimization techniques.',
                'start_date' => now()->addDays(60),
                'end_date' => now()->addDays(62),
                'location' => 'Training Room B',
                'price' => 149.99,
                'status' => TrainingConst::STATUS_ACTIVE,
                'max_capacity' => 25,
                'created_by' => $adminUser->id,
                'updated_by' => $adminUser->id,
            ],
        ];

        foreach ($trainings as $training) {
            Training::create($training);
        }

        $this->command->info('Training data seeded successfully.');
    }
} 