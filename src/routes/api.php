<?php

use App\Http\Controllers\API\V1\AuthController;
use App\Http\Controllers\API\V1\CampController;
use App\Http\Controllers\API\V1\TrainingController;
use App\Http\Middleware\ForceJson;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Apply <PERSON><PERSON>son middleware to all API routes
Route::get('/', function () {
    return response()->json(['message' => 'Welcome to the API!']);
});

Route::middleware(ForceJson::class)->group(function () {
    Route::prefix('v1')->group(function () {
        // Public Authentication routes with rate limiting
        Route::prefix('auth')->middleware('throttle:60,1')->group(function () {
            Route::post('/login', [AuthController::class, 'login']);
            Route::post('/refresh-token', [AuthController::class, 'refreshToken']);

            // Forgot Password routes
            Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
            Route::post('/verify-reset-otp', [AuthController::class, 'verifyResetOtp']);
            Route::post('/reset-password', [AuthController::class, 'resetPassword']);
        });

        // Protected Authentication routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::prefix('auth')->group(function () {
                Route::post('/logout', [AuthController::class, 'logout']);
                Route::post('/update-password', [AuthController::class, 'updatePassword']);
                Route::get('/profile', [AuthController::class, 'profile']);
                Route::put('/profile', [AuthController::class, 'updateProfile']);
            });

            // Root API for camps
            Route::prefix('camps')->group(function () {
                Route::get('/', [CampController::class, 'index']);
                Route::post('/', [CampController::class, 'store']);
                Route::get('{id}', [CampController::class, 'show']);
                Route::put('{id}', [CampController::class, 'update']);
                Route::delete('{id}', [CampController::class, 'destroy']);

                Route::post('{id}/duplicate', [CampController::class, 'duplicate']);
            });

            Route::prefix('trainings')->group(function () {
                Route::apiResource('', TrainingController::class)->parameters(['' => 'training']);

                Route::post('{id}/duplicate', [TrainingController::class, 'duplicate']);
                Route::put('{id}/status/{status}', [TrainingController::class, 'updateStatus']);
            });
        });
    });
});
