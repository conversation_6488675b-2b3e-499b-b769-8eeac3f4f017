grumphp:
  ascii:
    failed: ~
    succeeded: ~
  hide_circumvention_tip: true
  process_timeout: 300
  tasks:
    # Validate the composer json & lockfile
    composer:
      file: ./composer.json
      no_check_all: false
      no_check_lock: false
      no_check_publish: false
      no_local_repository: false
      with_dependencies: false
      strict: false
    # Ensure that debug statements never get committed
    git_blacklist:
      keywords:
        - "\/\/\\s\\+print_r("
        - "error_log("
        - "var_dump("
        - "dd("
        - "exit;"
      whitelist_patterns: [ ]
      triggered_by: [ 'php' ]
      regexp_type: G
      match_word: true
    # Attempt to enforce some basic standards for commit messages
    git_commit_message:
      allow_empty_message: false
      enforce_capitalized_subject: true
      enforce_single_lined_subject: true
      max_subject_width: 0
      max_body_width: 0
      matchers:
        'Please include a proper commit message': /\w+ \w+ \w+/
      multiline: false
    # Run basic PHP linting to ensure broken code is not committed
    phplint:
      exclude: [ 'bootstrap', 'vendor', 'database', 'storage' ]
      jobs: ~
      short_open_tag: false
      ignore_patterns: [ ]
      triggered_by: [ 'php', 'phtml', 'php3', 'php4', 'php5' ]
    # Enforce coding standards
    phpcs:
      standard: [ './phpcs.xml' ]
      encoding: 'utf-8'
      warning_severity: 0
      ignore_patterns:
        - vendor/
        - storage/
        - database/
        - bootstrap/cache/
    # Run linting on yaml files
    yamllint:
      whitelist_patterns: [ ]
      ignore_patterns: [ ]
      object_support: false
      exception_on_invalid_type: false
      parse_constant: false
      parse_custom_tags: false
    # Ensure we don't get massive files committed accidentally
    file_size:
      max_size: 10M
    securitychecker_enlightn:
      lockfile: ./composer.lock
      run_always: false
    # PHPMD has a number of rulesets we can apply, this currently just does
    # the most basic:
    #
    # https://github.com/phpro/grumphp/blob/master/doc/tasks/phpmd.md
    phpmd:
      report_format: text
      ruleset: [ './phpmd-ruleset' ]
      triggered_by: [ 'php' ]
      exclude:
        - vendor/
        - resources/
        - public/
        - config-seed/
        - database/
    # Run PHPStan static analysis of commits
    phpstan:
      memory_limit: "2048M"
      level: 0
