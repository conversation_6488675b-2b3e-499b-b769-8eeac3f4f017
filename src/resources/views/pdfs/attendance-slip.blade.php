<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Slip</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 28px;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 18px;
            font-weight: normal;
        }
        
        .content {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .info-section {
            margin-bottom: 25px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 12px;
            border-bottom: 1px dotted #bdc3c7;
            padding-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            width: 150px;
            color: #2c3e50;
        }
        
        .info-value {
            flex: 1;
            color: #34495e;
        }
        
        .status-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .status-present {
            border-left-color: #27ae60;
            background-color: #d5f4e6;
        }
        
        .status-absent {
            border-left-color: #e74c3c;
            background-color: #fdf2f2;
        }
        
        .status-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .health-notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .health-notes h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 16px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 20px;
        }
        
        .validation-stamp {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #e74c3c;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 12px;
            transform: rotate(15deg);
        }
        
        .notes-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .notes-section h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .notes-content {
            font-style: italic;
            color: #5a6c7d;
        }
    </style>
</head>
<body>
    <div class="validation-stamp">
        VALIDATED
    </div>
    
    <div class="header">
        <h1>Training Attendance Slip</h1>
        <h2>{{ $participant['training_name'] }}</h2>
    </div>
    
    <div class="content">
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">Participant Name:</div>
                <div class="info-value">{{ $participant['name'] }}</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">Age:</div>
                <div class="info-value">{{ $participant['age'] ?? 'Not specified' }} years old</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">Parent/Guardian:</div>
                <div class="info-value">{{ $participant['parent_guardian'] }}</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">Attendance Date:</div>
                <div class="info-value">{{ \Carbon\Carbon::parse($participant['attendance_date'])->format('F j, Y') }}</div>
            </div>
        </div>
        
        <div class="status-section {{ $participant['status'] === 'present' ? 'status-present' : 'status-absent' }}">
            <div class="status-title">
                Attendance Status: 
                <span style="text-transform: uppercase;">{{ $participant['status'] }}</span>
            </div>
        </div>
        
        <div class="health-notes">
            <h3>Health Notes & Medical Information</h3>
            <div>{{ $participant['health_notes'] }}</div>
        </div>
        
        @if($participant['notes'])
        <div class="notes-section">
            <h3>Additional Notes</h3>
            <div class="notes-content">{{ $participant['notes'] }}</div>
        </div>
        @endif
    </div>
    
    <div class="footer">
        <p>This attendance slip was generated on {{ $generated_at }}</p>
        <p><strong>This document is validated and cannot be modified.</strong></p>
    </div>
</body>
</html>
