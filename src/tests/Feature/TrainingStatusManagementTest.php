<?php

namespace Tests\Feature;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\FeatureTestCase;

class TrainingStatusManagementTest extends FeatureTestCase
{
    use RefreshDatabase;

    private User $admin;
    private Training $training;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create();
        $this->training = Training::factory()->create([
            'status' => TrainingConst::STATUS_ACTIVE,
            'created_by' => $this->admin->id,
            'updated_by' => $this->admin->id,
        ]);
    }

    public function test_admin_can_cancel_training(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_CANCELLED);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'status',
                    'created_at',
                    'updated_at'
                ]
            ]);

        $this->training->refresh();
        $this->assertEquals(TrainingConst::STATUS_CANCELLED, $this->training->status);
    }

    public function test_admin_can_delete_training_status(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_DELETED);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'status',
                    'created_at',
                    'updated_at'
                ]
            ]);

        $this->training->refresh();
        $this->assertEquals(TrainingConst::STATUS_DELETED, $this->training->status);
    }

    public function test_admin_can_update_training_status(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_FULL);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'status',
                    'created_at',
                    'updated_at'
                ]
            ]);

        $this->training->refresh();
        $this->assertEquals(TrainingConst::STATUS_FULL, $this->training->status);
    }

    public function test_cannot_update_nonexistent_training_status(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/999/status/" . TrainingConst::STATUS_CANCELLED);

        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message'
            ]);
    }

    public function test_cannot_cancel_nonexistent_training(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/999/status/" . TrainingConst::STATUS_CANCELLED);

        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message'
            ]);
    }

    public function test_cannot_delete_status_of_nonexistent_training(): void
    {
        $response = $this->actingAs($this->admin)
            ->putJson("/api/v1/trainings/999/status/" . TrainingConst::STATUS_DELETED);

        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message'
            ]);
    }

    public function test_training_status_methods_work_correctly(): void
    {
        // Test Active status
        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertTrue($this->training->isActive());
        $this->assertFalse($this->training->isCancelled());
        $this->assertFalse($this->training->isFull());
        $this->assertFalse($this->training->isCompleted());
        $this->assertFalse($this->training->isDeleted());
        $this->assertTrue($this->training->canAcceptRegistrations());

        // Test Cancelled status
        $this->training->status = TrainingConst::STATUS_CANCELLED;
        $this->assertFalse($this->training->isActive());
        $this->assertTrue($this->training->isCancelled());
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Test Full status
        $this->training->status = TrainingConst::STATUS_FULL;
        $this->assertTrue($this->training->isFull());
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Test Completed status
        $this->training->status = TrainingConst::STATUS_COMPLETED;
        $this->assertTrue($this->training->isCompleted());
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Test Deleted status
        $this->training->status = TrainingConst::STATUS_DELETED;
        $this->assertTrue($this->training->isDeleted());
        $this->assertFalse($this->training->canAcceptRegistrations());
    }

    public function test_training_past_end_date_detection(): void
    {
        // Test with past end date
        $this->training->end_date = now()->subDay();
        $this->assertTrue($this->training->isPastEndDate());

        // Test with future end date
        $this->training->end_date = now()->addDay();
        $this->assertFalse($this->training->isPastEndDate());
    }

    public function test_training_constants_are_correct(): void
    {
        $this->assertEquals('active', TrainingConst::STATUS_ACTIVE);
        $this->assertEquals('cancelled', TrainingConst::STATUS_CANCELLED);
        $this->assertEquals('full', TrainingConst::STATUS_FULL);
        $this->assertEquals('completed', TrainingConst::STATUS_COMPLETED);
        $this->assertEquals('deleted', TrainingConst::STATUS_DELETED);
    }

    public function test_training_status_labels_work(): void
    {
        $this->assertEquals('Active', TrainingConst::getStatusLabel(TrainingConst::STATUS_ACTIVE));
        $this->assertEquals('Cancelled', TrainingConst::getStatusLabel(TrainingConst::STATUS_CANCELLED));
        $this->assertEquals('Full', TrainingConst::getStatusLabel(TrainingConst::STATUS_FULL));
        $this->assertEquals('Completed', TrainingConst::getStatusLabel(TrainingConst::STATUS_COMPLETED));
        $this->assertEquals('Deleted', TrainingConst::getStatusLabel(TrainingConst::STATUS_DELETED));
        $this->assertEquals('Unknown', TrainingConst::getStatusLabel('invalid_status'));
    }

    public function test_training_status_french_labels_work(): void
    {
        $this->assertEquals('Actif', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_ACTIVE));
        $this->assertEquals('Annulé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_CANCELLED));
        $this->assertEquals('Complet', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_FULL));
        $this->assertEquals('Terminé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_COMPLETED));
        $this->assertEquals('Supprimé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_DELETED));
        $this->assertEquals('Inconnu', TrainingConst::getStatusLabelFr('invalid_status'));
    }

    public function test_training_status_list_contains_all_statuses(): void
    {
        $statuses = TrainingConst::getStatuses();
        
        $this->assertContains(TrainingConst::STATUS_ACTIVE, $statuses);
        $this->assertContains(TrainingConst::STATUS_CANCELLED, $statuses);
        $this->assertContains(TrainingConst::STATUS_FULL, $statuses);
        $this->assertContains(TrainingConst::STATUS_COMPLETED, $statuses);
        $this->assertContains(TrainingConst::STATUS_DELETED, $statuses);
        $this->assertCount(5, $statuses);
    }

    public function test_unauthorized_user_cannot_access_status_endpoints(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_CANCELLED);
        $response->assertStatus(200);

        $response = $this->actingAs($user)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_DELETED);
        $response->assertStatus(200);

        $response = $this->actingAs($user)
            ->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_FULL);
        $response->assertStatus(200);
    }

    public function test_unauthenticated_user_cannot_access_status_endpoints(): void
    {
        $response = $this->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_CANCELLED);
        $response->assertStatus(401);

        $response = $this->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_DELETED);
        $response->assertStatus(401);

        $response = $this->putJson("/api/v1/trainings/{$this->training->id}/status/" . TrainingConst::STATUS_FULL);
        $response->assertStatus(401);
    }
}
