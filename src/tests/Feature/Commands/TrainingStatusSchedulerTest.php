<?php

namespace Tests\Feature\Commands;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use App\Services\TrainingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Mockery;
use Tests\FeatureTestCase;

class TrainingStatusSchedulerTest extends FeatureTestCase
{
    use RefreshDatabase;

    private User $admin;
    private TrainingService $trainingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create();
        $this->trainingService = Mockery::mock(TrainingService::class);
        $this->app->instance(TrainingService::class, $this->trainingService);
    }

    public function test_scheduler_runs_training_status_update_command(): void
    {
        $this->trainingService->shouldReceive('checkAndUpdateTrainingStatuses')
            ->once()
            ->andReturn(null);

        $this->artisan('training:update-statuses')
            ->expectsOutput('Starting training status update...')
            ->expectsOutput('Training statuses updated successfully!')
            ->assertExitCode(0);
    }

    public function test_scheduler_handles_exceptions_gracefully(): void
    {
        $this->trainingService->shouldReceive('checkAndUpdateTrainingStatuses')
            ->once()
            ->andThrow(new \Exception('Database connection failed'));

        $this->artisan('training:update-statuses')
            ->expectsOutput('Starting training status update...')
            ->expectsOutput('Failed to update training statuses: Database connection failed')
            ->assertExitCode(1);
    }

    public function test_scheduler_command_is_registered(): void
    {
        $commands = Artisan::all();
        
        $this->assertArrayHasKey('training:update-statuses', $commands);
    }

    public function test_scheduler_command_has_correct_signature(): void
    {
        $this->artisan('training:update-statuses --help')
            ->expectsOutput('Update training statuses based on capacity and end dates')
            ->assertExitCode(0);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
