<?php

namespace Tests\Feature\Commands;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use App\Services\TrainingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\FeatureTestCase;

class UpdateTrainingStatusesCommandTest extends FeatureTestCase
{
    use RefreshDatabase;

    private User $admin;
    private TrainingService $trainingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create();
        $this->trainingService = Mockery::mock(TrainingService::class);
        $this->app->instance(TrainingService::class, $this->trainingService);
    }

    public function test_command_updates_training_statuses_successfully(): void
    {
        $this->trainingService->shouldReceive('checkAndUpdateTrainingStatuses')
            ->once()
            ->andReturn(null);

        $this->artisan('training:update-statuses')
            ->expectsOutput('Starting training status update...')
            ->expectsOutput('Training statuses updated successfully!')
            ->assertExitCode(0);
    }

    public function test_command_handles_exception_gracefully(): void
    {
        $this->trainingService->shouldReceive('checkAndUpdateTrainingStatuses')
            ->once()
            ->andThrow(new \Exception('Database connection failed'));

        $this->artisan('training:update-statuses')
            ->expectsOutput('Starting training status update...')
            ->expectsOutput('Failed to update training statuses: Database connection failed')
            ->assertExitCode(1);
    }

    public function test_command_signature_is_correct(): void
    {
        $this->artisan('training:update-statuses --help')
            ->expectsOutput('Update training statuses based on capacity and end dates')
            ->assertExitCode(0);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
