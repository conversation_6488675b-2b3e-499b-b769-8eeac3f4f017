<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class UpdateProfileTest extends FeatureTestCase
{
    use RefreshDatabase;

    public function test_user_can_update_profile_when_authenticated(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJ<PERSON>('/api/v1/auth/profile', [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'phone_number' => '+33123456789',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'phone_number',
                        'date_of_birth',
                        'status',
                    ],
                ],
            ]);
    }

    public function test_user_cannot_update_profile_without_authentication(): void
    {
        $response = $this->putJson('/api/v1/auth/profile', [
            'first_name' => 'John',
        ]);

        $response->assertStatus(401);
    }

    public function test_user_can_update_single_field(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'first_name' => 'Jane',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ]);

        $this->assertEquals('Jane', $response->json('data.user.first_name'));
    }

    public function test_user_cannot_update_to_existing_email(): void
    {
        // Create first user
        $user1 = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Create second user
        $user2 = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first user to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'email' => '<EMAIL>',
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'email',
                ],
            ]);
    }

    public function test_user_cannot_update_to_existing_phone_number(): void
    {
        // Create first user
        $user1 = User::factory()->create([
            'email' => '<EMAIL>',
            'phone_number' => '+33123456789',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Create second user
        $user2 = User::factory()->create([
            'email' => '<EMAIL>',
            'phone_number' => '+33987654321',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first user to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'phone_number' => '+33987654321',
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'phone_number',
                ],
            ]);
    }

    public function test_user_can_update_date_of_birth(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'date_of_birth' => '1990-01-01',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'user_profile_updated_successfully',
            ]);

        $this->assertEquals('1990-01-01', $response->json('data.user.date_of_birth'));
    }

    public function test_user_cannot_update_with_invalid_email_format(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'email' => 'invalid-email',
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'email',
                ],
            ]);
    }

    public function test_user_cannot_update_with_invalid_phone_format(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Login first to get token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->putJson('/api/v1/auth/profile', [
                'phone_number' => 'invalid-phone',
            ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'phone_number',
                ],
            ]);
    }
}
