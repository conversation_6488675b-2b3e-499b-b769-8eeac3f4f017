<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class TrainingDuplicateTest extends FeatureTestCase
{
    protected Training $training;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 'active',
        ]);

        $this->training = Training::factory()->create([
            'name' => 'Original Training',
            'description' => 'Original Description',
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
            'status' => TrainingConst::STATUS_ACTIVE,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);
    }

    public function test_admin_can_duplicate_training_with_valid_data(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(12)->format('Y-m-d'),
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'location',
                    'max_capacity',
                    'price',
                    'status',
                    'start_date',
                    'end_date',
                    'creator',
                    'updater',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $this->assertDatabaseHas('trainings', [
            'name' => 'Original Training',
            'description' => 'Original Description',
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'status' => TrainingConst::STATUS_ACTIVE,
        ]);
    }

    public function test_duplicate_training_requires_dates(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start_date', 'end_date']);
    }

    public function test_duplicate_training_creates_new_record_with_different_id(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(12)->format('Y-m-d'),
            ]);

        $response->assertStatus(201);

        $duplicatedTraining = Training::where('name', 'Original Training')
            ->where('id', '!=', $this->training->id)
            ->first();

        $this->assertNotNull($duplicatedTraining);
        $this->assertNotEquals($this->training->id, $duplicatedTraining->id);
    }

    public function test_duplicate_training_preserves_all_data_except_system_fields(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(12)->format('Y-m-d'),
            ]);

        $response->assertStatus(201);

        $duplicatedTraining = Training::where('name', 'Original Training')
            ->where('id', '!=', $this->training->id)
            ->first();

        $this->assertEquals($this->training->name, $duplicatedTraining->name);
        $this->assertEquals($this->training->description, $duplicatedTraining->description);
        $this->assertEquals($this->training->location, $duplicatedTraining->location);
        $this->assertEquals($this->training->max_capacity, $duplicatedTraining->max_capacity);
        $this->assertEquals($this->training->price, $duplicatedTraining->price);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicatedTraining->status);
    }

    public function test_duplicate_training_with_invalid_start_date(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->subDays(1)->format('Y-m-d'),
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start_date']);
    }

    public function test_duplicate_training_with_invalid_end_date(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(5)->format('Y-m-d'),
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end_date']);
    }

    public function test_duplicate_training_with_invalid_date_format(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => 'invalid-date',
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start_date']);
    }

    public function test_duplicate_nonexistent_training(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/99999/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(12)->format('Y-m-d'),
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'training_not_found',
            ]);
    }

    public function test_unauthenticated_user_cannot_duplicate_training(): void
    {
        $response = $this->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
        ]);

        $response->assertStatus(401);
    }

    public function test_duplicate_training_sets_correct_user_audit_fields(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
                'end_date' => now()->addDays(12)->format('Y-m-d'),
            ]);

        $response->assertStatus(201);

        $duplicatedTraining = Training::where('name', 'Original Training')
            ->where('id', '!=', $this->training->id)
            ->first();

        $this->assertEquals($this->user->id, $duplicatedTraining->created_by);
        $this->assertEquals($this->user->id, $duplicatedTraining->updated_by);
    }

    public function test_duplicate_training_with_missing_end_date_fails_validation(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/trainings/{$this->training->id}/duplicate", [
                'start_date' => now()->addDays(10)->format('Y-m-d'),
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end_date']);
    }
}
