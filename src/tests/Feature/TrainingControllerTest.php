<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Training;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\FeatureTestCase;

class TrainingControllerTest extends FeatureTestCase
{
    use RefreshDatabase;

    public User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    public function test_can_list_trainings()
    {
        $user = User::factory()->create();

        Training::factory()->count(3)->create([
            'created_by' => $user->id,
            'updated_by' => $user->id,
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/v1/trainings');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'start_date',
                        'end_date',
                        'location',
                        'price',
                        'status',
                        'max_capacity',
                        'creator',
                        'updater',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);
    }

    public function test_can_create_training()
    {
        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(30)->toDateTimeString(),
            'end_date' => now()->addDays(32)->toDateTimeString(),
            'location' => 'Test Location',
            'price' => 199,
            'max_capacity' => 25,
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/trainings', $trainingData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'start_date',
                    'end_date',
                    'location',
                    'price',
                    'status',
                    'max_capacity',
                ],
            ]);

        $this->assertDatabaseHas('trainings', [
            'name' => 'Test Training',
            'created_by' => $this->user->id,
        ]);
    }

    public function test_can_show_training()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/trainings/{$training->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'description',
                    'start_date',
                    'end_date',
                    'location',
                    'price',
                    'status',
                    'max_capacity',
                ],
            ]);
    }

    public function test_can_update_training()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $updateData = [
            'name' => 'Updated Training Name',
            'price' => 299,
        ];

        $response = $this->actingAs($this->user)
            ->putJson("/api/v1/trainings/{$training->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('trainings', [
            'id' => $training->id,
            'name' => 'Updated Training Name',
            'price' => 299,
        ]);
    }

    public function test_can_delete_training()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/api/v1/trainings/{$training->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('trainings', [
            'id' => $training->id,
        ]);
    }

    public function test_can_search_trainings_by_name()
    {
        Training::factory()->create([
            'name' => 'Laravel Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'PHP Basics',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/trainings?name=Laravel');

        $response->assertStatus(200);
        $responseData = $response->json('data');

        $this->assertCount(1, $responseData);
        $this->assertEquals('Laravel Training', $responseData[0]['name']);
    }

    // Failure Cases

    public function test_cannot_create_training_without_authentication()
    {
        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(30)->toDateTimeString(),
            'end_date' => now()->addDays(32)->toDateTimeString(),
            'location' => 'Test Location',
            'price' => 199.99,
            'max_capacity' => 25,
        ];

        $response = $this->postJson('/api/v1/trainings', $trainingData);

        $response->assertStatus(401);
    }

    public function test_cannot_create_training_with_invalid_data()
    {
        $invalidData = [
            'name' => '', // Empty name
            'description' => 'Test Description',
            'start_date' => now()->subDays(1)->toDateTimeString(), // Past date
            'end_date' => now()->addDays(32)->toDateTimeString(),
            'location' => 'Test Location',
            'price' => -100, // Negative price
            'max_capacity' => 0, // Zero capacity
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/trainings', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'start_date', 'price', 'max_capacity']);
    }

    public function test_cannot_create_training_with_end_date_before_start_date()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(30)->toDateTimeString(),
            'end_date' => now()->addDays(25)->toDateTimeString(), // Before start date
            'location' => 'Test Location',
            'price' => 199.99,
            'max_capacity' => 25,
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/trainings', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end_date']);
    }

    public function test_cannot_show_nonexistent_training()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/trainings/99999');

        $response->assertStatus(404);
    }

    public function test_cannot_update_nonexistent_training()
    {
        $updateData = [
            'name' => 'Updated Training Name',
            'price' => 299,
        ];

        $response = $this->actingAs($this->user)
            ->putJson('/api/v1/trainings/99999', $updateData);

        $response->assertStatus(404);
    }

    public function test_cannot_update_training_with_invalid_data()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $invalidData = [
            'name' => '', // Empty name
            'price' => -50, // Negative price
            'max_capacity' => 0, // Zero capacity
        ];

        $response = $this->actingAs($this->user)
            ->putJson("/api/v1/trainings/{$training->id}", $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'price', 'max_capacity']);
    }

    public function test_cannot_delete_nonexistent_training()
    {
        $response = $this->actingAs($this->user)
            ->deleteJson('/api/v1/trainings/99999');

        $response->assertStatus(404);
    }

    public function test_cannot_access_training_endpoints_without_authentication()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        // Test list trainings
        $response = $this->getJson('/api/v1/trainings');
        $response->assertStatus(401);

        // Test show training
        $response = $this->getJson("/api/v1/trainings/{$training->id}");
        $response->assertStatus(401);

        // Test update training
        $response = $this->putJson("/api/v1/trainings/{$training->id}", ['name' => 'Updated']);
        $response->assertStatus(401);

        // Test delete training
        $response = $this->deleteJson("/api/v1/trainings/{$training->id}");
        $response->assertStatus(401);
    }

    public function test_search_trainings_returns_empty_when_no_matches()
    {
        Training::factory()->create([
            'name' => 'Laravel Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/trainings?name=NonexistentTraining');

        $response->assertStatus(200);
        $responseData = $response->json('data');

        $this->assertCount(0, $responseData);
    }

    public function test_cannot_create_training_with_description_over_limit()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => str_repeat('a', 501), // Over 500 character limit
            'start_date' => now()->addDays(30)->toDateTimeString(),
            'end_date' => now()->addDays(32)->toDateTimeString(),
            'location' => 'Test Location',
            'price' => 199,
            'max_capacity' => 25,
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/trainings', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['description']);
    }

    public function test_cannot_create_training_with_name_over_limit()
    {
        $invalidData = [
            'name' => str_repeat('a', 256), // Over 255 character limit
            'description' => 'Test Description',
            'start_date' => now()->addDays(30)->toDateTimeString(),
            'end_date' => now()->addDays(32)->toDateTimeString(),
            'location' => 'Test Location',
            'price' => 199,
            'max_capacity' => 25,
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/trainings', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }
}
