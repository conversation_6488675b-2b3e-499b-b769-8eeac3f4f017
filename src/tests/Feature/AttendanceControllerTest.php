<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Const\Training as TrainingConst;
use App\Enums\AttendanceStatus;
use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationStatus;
use App\Models\Attendance;
use App\Models\Child;
use App\Models\Registration;
use App\Models\Training;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\FeatureTestCase;

class AttendanceControllerTest extends FeatureTestCase
{
    use RefreshDatabase;

    private User $admin;
    private Training $training;
    private User $participant;
    private Child $child;
    private Registration $registration;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['status' => 'active']);
        $this->participant = User::factory()->create(['status' => 'active']);
        $this->child = Child::factory()->create(['guardian_user_id' => $this->participant->id]);
        
        $this->training = Training::factory()->create([
            'status' => TrainingConst::STATUS_ACTIVE,
            'start_date' => Carbon::today()->subDays(5),
            'end_date' => Carbon::today()->addDays(5),
        ]);

        $this->registration = Registration::factory()->create([
            'user_id' => $this->participant->id,
            'child_id' => $this->child->id,
            'type' => RegistrationSourceType::TRAINING,
            'source_id' => $this->training->id,
            'status' => RegistrationStatus::APPROVED,
        ]);
    }

    public function test_can_get_attendance_data_for_active_training(): void
    {
        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance");

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'training' => ['id', 'name', 'start_date', 'end_date', 'status'],
                    'date',
                    'attendance_data' => [
                        '*' => [
                            'registration_id',
                            'user_id',
                            'user_name',
                            'child_name',
                            'is_present',
                            'notes',
                            'is_validated',
                        ]
                    ],
                    'is_complete',
                    'can_validate',
                ]
            ]);

        $this->assertFalse($response->json('data.is_complete'));
        $this->assertFalse($response->json('data.can_validate'));
    }

    public function test_cannot_access_attendance_for_inactive_training(): void
    {
        $this->training->update(['status' => TrainingConst::STATUS_CANCELLED]);

        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance");

        $response->assertForbidden()
            ->assertJson([
                'success' => false,
                'message' => 'attendance_access_denied',
            ]);
    }

    public function test_cannot_access_attendance_for_date_outside_training_range(): void
    {
        $futureDate = Carbon::today()->addDays(10)->format('Y-m-d');

        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance?date={$futureDate}");

        $response->assertForbidden()
            ->assertJson([
                'success' => false,
                'message' => 'attendance_access_denied',
            ]);
    }

    public function test_can_record_attendance_successfully(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = [
            'date' => $date,
            'attendance_data' => [
                [
                    'registration_id' => $this->registration->id,
                    'is_present' => true,
                    'notes' => 'On time',
                ]
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson("/api/v1/trainings/{$this->training->id}/attendance", $attendanceData);

        $response->assertCreated()
            ->assertJson([
                'success' => true,
                'message' => 'attendance_recorded_successfully',
            ]);

        $this->assertDatabaseHas('attendances', [
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => 'present',
            'notes' => 'On time',
            'is_validated' => true,
            'validated_by' => $this->admin->id,
        ]);
    }

    public function test_cannot_record_attendance_without_all_participants_marked(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = [
            'date' => $date,
            'attendance_data' => [] // Empty data
        ];

        $response = $this->actingAs($this->admin)
            ->postJson("/api/v1/trainings/{$this->training->id}/attendance", $attendanceData);

        $response->assertBadRequest()
            ->assertJson([
                'success' => false,
                'message' => 'attendance_incomplete',
            ]);
    }

    public function test_cannot_record_attendance_for_validated_session(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        // Create validated attendance
        Attendance::factory()->create([
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => AttendanceStatus::PRESENT,
            'is_validated' => true,
            'validated_by' => $this->admin->id,
            'validated_at' => now(),
        ]);

        $attendanceData = [
            'date' => $date,
            'attendance_data' => [
                [
                    'registration_id' => $this->registration->id,
                    'is_present' => false,
                ]
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson("/api/v1/trainings/{$this->training->id}/attendance", $attendanceData);

        $response->assertForbidden()
            ->assertJson([
                'success' => false,
                'message' => 'attendance_access_denied',
            ]);
    }

    public function test_can_check_attendance_status(): void
    {
        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance/status");

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'can_record',
                    'errors',
                    'is_complete',
                    'can_validate',
                ]
            ]);

        $this->assertTrue($response->json('data.can_record'));
        $this->assertFalse($response->json('data.is_complete'));
        $this->assertFalse($response->json('data.can_validate'));
    }

    public function test_attendance_complete_when_all_participants_marked(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        // Create attendance record
        Attendance::factory()->create([
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => AttendanceStatus::PRESENT,
            'is_validated' => false,
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance/status?date={$date}");

        $response->assertOk();
        $this->assertTrue($response->json('data.is_complete'));
        $this->assertTrue($response->json('data.can_validate'));
    }

    public function test_default_date_is_today_when_within_training_range(): void
    {
        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance");

        $expectedDate = Carbon::today()->format('Y-m-d');
        $this->assertEquals($expectedDate, $response->json('data.date'));
    }

    public function test_default_date_is_training_start_when_today_outside_range(): void
    {
        $this->training->update([
            'start_date' => Carbon::today()->addDays(10),
            'end_date' => Carbon::today()->addDays(15),
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/{$this->training->id}/attendance");

        $expectedDate = Carbon::today()->addDays(10)->format('Y-m-d');
        $this->assertEquals($expectedDate, $response->json('data.date'));
    }

    public function test_returns_404_for_nonexistent_training(): void
    {
        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/trainings/99999/attendance");

        $response->assertNotFound()
            ->assertJson([
                'success' => false,
                'message' => 'training_not_found',
            ]);
    }

    public function test_validation_requires_all_fields(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = [
            'date' => $date,
            'attendance_data' => [
                [
                    'registration_id' => $this->registration->id,
                    // Missing status_id
                ]
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson("/api/v1/trainings/{$this->training->id}/attendance", $attendanceData);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['attendance_data.0.is_present']);
    }
}
