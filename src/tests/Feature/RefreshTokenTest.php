<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class RefreshTokenTest extends FeatureTestCase
{
    use RefreshDatabase;

    public function test_user_can_refresh_token_with_valid_refresh_token(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // First login to get refresh token
        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $loginResponse->assertStatus(200);
        $refreshToken = $loginResponse->json('data.refresh_token');

        // Debug: Check if refresh token exists and has proper length
        $this->assertNotEmpty($refreshToken, 'Refresh token should not be empty');
        $this->assertGreaterThanOrEqual(50, strlen($refreshToken), 'Refresh token should be at least 50 characters');

        // Now test refresh token
        $response = $this->withHeader('Authorization', 'Bearer ' . $loginResponse->json('data.access_token'))
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => $refreshToken,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'access_token',
                    'refresh_token',
                    'token_type',
                ],
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('token_refreshed_successfully', $response->json('message'));
    }

    public function test_refresh_token_fails_with_invalid_token(): void
    {
        // First login to get access token
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $loginResponse = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Test@123',
        ]);

        $accessToken = $loginResponse->json('data.access_token');

        $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
            ->postJson('/api/v1/auth/refresh-token', [
                'refresh_token' => 'invalid_token_that_does_not_exist_in_database_should_be_long_enough_for_validation_minimum_50_chars',
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'validation_failed',
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'refresh_token',
                ],
            ]);
    }

    public function test_refresh_token_fails_without_token(): void
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', []);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'refresh_token',
                ],
            ]);
    }

    public function test_refresh_token_fails_with_short_token(): void
    {
        $response = $this->postJson('/api/v1/auth/refresh-token', [
            'refresh_token' => 'short',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'refresh_token',
                ],
            ]);
    }
}
