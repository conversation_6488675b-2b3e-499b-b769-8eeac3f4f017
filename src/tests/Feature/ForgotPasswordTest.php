<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use App\Services\OtpService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\FeatureTestCase;

class ForgotPasswordTest extends FeatureTestCase
{
    use RefreshDatabase;

    public function test_user_can_initiate_forgot_password_with_valid_email(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'email',
                    'expires_in',
                    'remaining_attempts',
                ],
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('otp_sent_successfully', $response->json('message'));
    }

    public function test_user_cannot_initiate_forgot_password_with_invalid_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'email',
                ],
            ]);
    }

    public function test_user_cannot_initiate_forgot_password_with_nonexistent_email(): void
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'email',
                ],
            ]);
    }

    public function test_user_cannot_initiate_forgot_password_with_inactive_account(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'inactive',
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'email',
                ],
            ]);
    }

    public function test_user_can_verify_reset_otp_with_valid_data(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Mock the OTP service to return a known OTP
        $this->mock(OtpService::class, function ($mock) {
            $mock->shouldReceive('verifyOtp')
                ->once()
                ->with('<EMAIL>', '123456', OtpService::TYPE_RESET)
                ->andReturn(true);
        });

        $response = $this->postJson('/api/v1/auth/verify-reset-otp', [
            'email' => '<EMAIL>',
            'otp' => '123456',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'email',
                    'verified',
                    'reset_token',
                    'reset_token_expires_in',
                ],
            ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals('otp_verified_successfully', $response->json('message'));
    }

    public function test_user_cannot_verify_reset_otp_with_invalid_otp(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        $response = $this->postJson('/api/v1/auth/verify-reset-otp', [
            'email' => '<EMAIL>',
            'otp' => '000000',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'otp',
                ],
            ]);
    }

    public function test_user_can_reset_password_with_valid_token(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('Test@123'),
            'status' => 'active',
        ]);

        // Mock the AuthService to return a successful result
        $this->mock(\App\Services\AuthService::class, function ($mock) {
            $mock->shouldReceive('resetPassword')
                ->once()
                ->with('<EMAIL>', 'NewPassword123!', 'valid_reset_token_123')
                ->andReturn([
                    'success' => true,
                    'message' => 'Password reset successfully',
                ]);
        });

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
            'token' => 'valid_reset_token_123',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'password_reset_success',
            ]);
    }

    public function test_user_cannot_reset_password_with_invalid_token(): void
    {
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
            'token' => 'invalid_token',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
            ]);
    }

    public function test_user_cannot_reset_password_with_weak_password(): void
    {
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'weak',
            'password_confirmation' => 'weak',
            'token' => 'valid_token',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'password',
                ],
            ]);
    }

    public function test_user_cannot_reset_password_with_mismatched_confirmation(): void
    {
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'DifferentPassword123!',
            'token' => 'valid_token',
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors' => [
                    'password',
                ],
            ]);
    }
}
