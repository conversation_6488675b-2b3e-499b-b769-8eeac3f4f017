<?php

namespace Tests\Unit\Const;

use App\Const\Training as TrainingConst;
use Tests\TestCase;

class TrainingConstTest extends TestCase
{
    public function test_status_constants_are_defined(): void
    {
        $this->assertEquals('active', TrainingConst::STATUS_ACTIVE);
        $this->assertEquals('cancelled', TrainingConst::STATUS_CANCELLED);
        $this->assertEquals('full', TrainingConst::STATUS_FULL);
        $this->assertEquals('completed', TrainingConst::STATUS_COMPLETED);
        $this->assertEquals('deleted', TrainingConst::STATUS_DELETED);
    }

    public function test_get_statuses_returns_all_statuses(): void
    {
        $statuses = TrainingConst::getStatuses();

        $this->assertIsArray($statuses);
        $this->assertCount(5, $statuses);
        $this->assertContains(TrainingConst::STATUS_ACTIVE, $statuses);
        $this->assertContains(TrainingConst::STATUS_CANCELLED, $statuses);
        $this->assertContains(TrainingConst::STATUS_FULL, $statuses);
        $this->assertContains(TrainingConst::STATUS_COMPLETED, $statuses);
        $this->assertContains(TrainingConst::STATUS_DELETED, $statuses);
    }

    public function test_get_status_label_returns_correct_labels(): void
    {
        $this->assertEquals('Active', TrainingConst::getStatusLabel(TrainingConst::STATUS_ACTIVE));
        $this->assertEquals('Cancelled', TrainingConst::getStatusLabel(TrainingConst::STATUS_CANCELLED));
        $this->assertEquals('Full', TrainingConst::getStatusLabel(TrainingConst::STATUS_FULL));
        $this->assertEquals('Completed', TrainingConst::getStatusLabel(TrainingConst::STATUS_COMPLETED));
        $this->assertEquals('Deleted', TrainingConst::getStatusLabel(TrainingConst::STATUS_DELETED));
    }

    public function test_get_status_label_returns_unknown_for_invalid_status(): void
    {
        $this->assertEquals('Unknown', TrainingConst::getStatusLabel('invalid_status'));
        $this->assertEquals('Unknown', TrainingConst::getStatusLabel(''));
        $this->assertEquals('Unknown', TrainingConst::getStatusLabel(null));
    }

    public function test_get_status_label_fr_returns_correct_french_labels(): void
    {
        $this->assertEquals('Actif', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_ACTIVE));
        $this->assertEquals('Annulé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_CANCELLED));
        $this->assertEquals('Complet', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_FULL));
        $this->assertEquals('Terminé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_COMPLETED));
        $this->assertEquals('Supprimé', TrainingConst::getStatusLabelFr(TrainingConst::STATUS_DELETED));
    }

    public function test_get_status_label_fr_returns_unknown_for_invalid_status(): void
    {
        $this->assertEquals('Inconnu', TrainingConst::getStatusLabelFr('invalid_status'));
        $this->assertEquals('Inconnu', TrainingConst::getStatusLabelFr(''));
        $this->assertEquals('Inconnu', TrainingConst::getStatusLabelFr(null));
    }

    public function test_status_constants_are_unique(): void
    {
        $statuses = TrainingConst::getStatuses();
        $uniqueStatuses = array_unique($statuses);

        $this->assertEquals(count($statuses), count($uniqueStatuses), 'All status constants should be unique');
    }

    public function test_status_constants_are_strings(): void
    {
        $this->assertIsString(TrainingConst::STATUS_ACTIVE);
        $this->assertIsString(TrainingConst::STATUS_CANCELLED);
        $this->assertIsString(TrainingConst::STATUS_FULL);
        $this->assertIsString(TrainingConst::STATUS_COMPLETED);
        $this->assertIsString(TrainingConst::STATUS_DELETED);
    }

    public function test_status_labels_are_strings(): void
    {
        $statuses = TrainingConst::getStatuses();

        foreach ($statuses as $status) {
            $this->assertIsString(TrainingConst::getStatusLabel($status));
            $this->assertIsString(TrainingConst::getStatusLabelFr($status));
        }
    }

    public function test_status_labels_are_not_empty(): void
    {
        $statuses = TrainingConst::getStatuses();

        foreach ($statuses as $status) {
            $this->assertNotEmpty(TrainingConst::getStatusLabel($status));
            $this->assertNotEmpty(TrainingConst::getStatusLabelFr($status));
        }
    }
}
