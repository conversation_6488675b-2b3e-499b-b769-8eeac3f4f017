<?php

namespace Tests\Unit\Models;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use Tests\TestCase;

class TrainingStatusTest extends TestCase
{
    private User $admin;
    private Training $training;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create();
        $this->training = Training::factory()->create([
            'status' => TrainingConst::STATUS_ACTIVE,
            'created_by' => $this->admin->id,
            'updated_by' => $this->admin->id,
        ]);
    }

    public function test_is_active_method(): void
    {
        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertTrue($this->training->isActive());

        $this->training->status = TrainingConst::STATUS_CANCELLED;
        $this->assertFalse($this->training->isActive());
    }

    public function test_is_cancelled_method(): void
    {
        $this->training->status = TrainingConst::STATUS_CANCELLED;
        $this->assertTrue($this->training->isCancelled());

        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertFalse($this->training->isCancelled());
    }

    public function test_is_full_method(): void
    {
        $this->training->status = TrainingConst::STATUS_FULL;
        $this->assertTrue($this->training->isFull());

        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertFalse($this->training->isFull());
    }

    public function test_is_completed_method(): void
    {
        $this->training->status = TrainingConst::STATUS_COMPLETED;
        $this->assertTrue($this->training->isCompleted());

        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertFalse($this->training->isCompleted());
    }

    public function test_is_deleted_method(): void
    {
        $this->training->status = TrainingConst::STATUS_DELETED;
        $this->assertTrue($this->training->isDeleted());

        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertFalse($this->training->isDeleted());
    }

    public function test_can_accept_registrations_method(): void
    {
        // Active training can accept registrations
        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertTrue($this->training->canAcceptRegistrations());

        // Cancelled training cannot accept registrations
        $this->training->status = TrainingConst::STATUS_CANCELLED;
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Full training cannot accept registrations
        $this->training->status = TrainingConst::STATUS_FULL;
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Completed training cannot accept registrations
        $this->training->status = TrainingConst::STATUS_COMPLETED;
        $this->assertFalse($this->training->canAcceptRegistrations());

        // Deleted training cannot accept registrations
        $this->training->status = TrainingConst::STATUS_DELETED;
        $this->assertFalse($this->training->canAcceptRegistrations());
    }

    public function test_is_past_end_date_method(): void
    {
        // Training with past end date
        $this->training->end_date = now()->subDay();
        $this->assertTrue($this->training->isPastEndDate());

        // Training with future end date
        $this->training->end_date = now()->addDay();
        $this->assertFalse($this->training->isPastEndDate());

        // Training with current end date
        $this->training->end_date = now();
        $this->assertTrue($this->training->isPastEndDate());
    }

    public function test_training_relationships(): void
    {
        $this->assertInstanceOf(User::class, $this->training->creator);
        $this->assertInstanceOf(User::class, $this->training->updater);
        $this->assertEquals($this->admin->id, $this->training->creator->id);
        $this->assertEquals($this->admin->id, $this->training->updater->id);
    }

    public function test_training_fillable_attributes(): void
    {
        $fillable = [
            'name',
            'description',
            'start_date',
            'end_date',
            'location',
            'price',
            'status',
            'max_capacity',
            'created_by',
            'updated_by',
        ];

        $this->assertEquals($fillable, $this->training->getFillable());
    }

    public function test_training_casts(): void
    {
        $casts = [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'price' => 'decimal:2',
            'max_capacity' => 'integer',
            'created_by' => 'string',
            'updated_by' => 'string',
        ];

        $this->assertEquals($casts, $this->training->getCasts());
    }

    public function test_training_status_transitions(): void
    {
        // Test status transitions
        $this->training->status = TrainingConst::STATUS_ACTIVE;
        $this->assertTrue($this->training->isActive());
        $this->assertTrue($this->training->canAcceptRegistrations());

        $this->training->status = TrainingConst::STATUS_CANCELLED;
        $this->assertTrue($this->training->isCancelled());
        $this->assertFalse($this->training->canAcceptRegistrations());

        $this->training->status = TrainingConst::STATUS_FULL;
        $this->assertTrue($this->training->isFull());
        $this->assertFalse($this->training->canAcceptRegistrations());

        $this->training->status = TrainingConst::STATUS_COMPLETED;
        $this->assertTrue($this->training->isCompleted());
        $this->assertFalse($this->training->canAcceptRegistrations());

        $this->training->status = TrainingConst::STATUS_DELETED;
        $this->assertTrue($this->training->isDeleted());
        $this->assertFalse($this->training->canAcceptRegistrations());
    }
}
