<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Enums\CampStatus;
use App\Models\Camp;
use App\Repositories\CampRepository;
use App\Services\CampService;

class CampDuplicateTest extends UnitTestCase
{
    protected CampRepository $campRepository;
    protected CampService $campService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->campRepository = app(CampRepository::class);
        $this->campService = app(CampService::class);
    }

    public function test_duplicate_camp_creates_new_camp_with_same_data(): void
    {
        $camp = Camp::factory()->create([
            'name' => 'Original Camp',
            'description' => 'Original Description',
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
        ];

        $duplicate = $this->campRepository->duplicate($camp, $duplicateData);

        $this->assertNotEquals($camp->id, $duplicate->id);
        $this->assertEquals($camp->name, $duplicate->name);
        $this->assertEquals($camp->description, $duplicate->description);
        $this->assertEquals($camp->location, $duplicate->location);
        $this->assertEquals($camp->max_capacity, $duplicate->max_capacity);
        $this->assertEquals($camp->price, $duplicate->price);
        $this->assertEquals(CampStatus::Active, $duplicate->status);
        $this->assertEquals($duplicateData['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($duplicateData['end_date'], $duplicate->end_date->format('Y-m-d'));
    }

    public function test_duplicate_camp_does_not_duplicate_id(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);
        $duplicate = $this->campRepository->duplicate($camp, []);
        $this->assertNotEquals($camp->id, $duplicate->id);
    }

    public function test_duplicate_camp_does_not_duplicate_timestamps(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        // Add a small delay to ensure different timestamps
        sleep(1);

        $duplicate = $this->campRepository->duplicate($camp, []);

        $this->assertNotEquals($camp->created_at, $duplicate->created_at);
        $this->assertNotEquals($camp->updated_at, $duplicate->updated_at);
    }

    public function test_duplicate_camp_sets_status_to_active(): void
    {
        $camp = Camp::factory()->create([
            'status' => CampStatus::Cancelled,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);
        $duplicate = $this->campRepository->duplicate($camp, []);
        $this->assertEquals(CampStatus::Active, $duplicate->status);
    }

    public function test_duplicate_camp_with_custom_dates(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);
        $customDates = [
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(8)->format('Y-m-d'),
        ];

        $duplicate = $this->campRepository->duplicate($camp, $customDates);

        $this->assertEquals($customDates['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($customDates['end_date'], $duplicate->end_date->format('Y-m-d'));
    }

    public function test_duplicate_camp_without_custom_dates(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicate = $this->campRepository->duplicate($camp, []);

        $this->assertEquals($camp->start_date->format('Y-m-d'), $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($camp->end_date->format('Y-m-d'), $duplicate->end_date->format('Y-m-d'));
    }

    public function test_duplicate_camp_service_throws_exception_when_not_authenticated(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);
        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unauthorized: No authenticated user found.');

        $this->campService->duplicate($duplicateData, $camp);
    }

    public function test_duplicate_camp_preserves_all_fields_except_system_fields(): void
    {
        $camp = Camp::factory()->create([
            'name' => 'Test Camp',
            'description' => 'Test Description',
            'internal_note' => 'Test Internal Note',
            'location' => 'Test Location',
            'max_capacity' => 75,
            'price' => 1500,
            'status' => CampStatus::Cancelled,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicate = $this->campRepository->duplicate($camp, []);

        $this->assertEquals($camp->name, $duplicate->name);
        $this->assertEquals($camp->description, $duplicate->description);
        $this->assertEquals($camp->internal_note, $duplicate->internal_note);
        $this->assertEquals($camp->location, $duplicate->location);
        $this->assertEquals($camp->max_capacity, $duplicate->max_capacity);
        $this->assertEquals($camp->price, $duplicate->price);
        $this->assertEquals(CampStatus::Active, $duplicate->status); // Status should be reset to active
    }

    public function test_duplicate_camp_creates_unique_record(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicate1 = $this->campRepository->duplicate($camp, []);
        $duplicate2 = $this->campRepository->duplicate($camp, []);

        $this->assertNotEquals($camp->id, $duplicate1->id);
        $this->assertNotEquals($camp->id, $duplicate2->id);
        $this->assertNotEquals($duplicate1->id, $duplicate2->id);
    }

    public function test_duplicate_camp_with_invalid_start_date_before_today(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $invalidDates = [
            'start_date' => now()->subDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(2)->format('Y-m-d'),
        ];

        $request = new \App\Http\Requests\API\V1\Camps\DuplicateCampRequest();
        $request->merge($invalidDates);

        $validator = \Validator::make($invalidDates, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
    }

    public function test_duplicate_camp_with_end_date_before_start_date(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $invalidDates = [
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(3)->format('Y-m-d'),
        ];

        $request = new \App\Http\Requests\API\V1\Camps\DuplicateCampRequest();
        $request->merge($invalidDates);

        $validator = \Validator::make($invalidDates, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_update_camp_cannot_update_start_date_and_end_date(): void
    {
        $camp = Camp::factory()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $updateData = [
            'name' => 'Updated Camp Name',
            'description' => 'Updated Description',
            'location' => 'Updated Location',
            'max_capacity' => 100,
            'price' => 2000,
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(15)->format('Y-m-d'),
        ];

        $request = new \App\Http\Requests\API\V1\Camps\UpdateCampRequest();
        $request->merge($updateData);

        $validator = \Validator::make($updateData, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_store_camp_requires_start_date_and_end_date(): void
    {
        $validData = [
            'name' => 'Test Camp',
            'description' => 'Test Description',
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'status' => 'active',
        ];

        $request = new \App\Http\Requests\API\V1\Camps\StoreCampRequest();
        $request->merge($validData);

        $validator = \Validator::make($validData, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_store_camp_with_invalid_start_date_before_today(): void
    {
        $invalidData = [
            'name' => 'Test Camp',
            'description' => 'Test Description',
            'start_date' => now()->subDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(2)->format('Y-m-d'),
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'status' => 'active',
        ];

        $request = new \App\Http\Requests\API\V1\Camps\StoreCampRequest();
        $request->merge($invalidData);

        $validator = \Validator::make($invalidData, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
    }

    public function test_store_camp_with_end_date_before_start_date(): void
    {
        $invalidData = [
            'name' => 'Test Camp',
            'description' => 'Test Description',
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(3)->format('Y-m-d'),
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'status' => 'active',
        ];

        $request = new \App\Http\Requests\API\V1\Camps\StoreCampRequest();
        $request->merge($invalidData);

        $validator = \Validator::make($invalidData, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_store_camp_with_valid_data(): void
    {
        $validData = [
            'name' => 'Test Camp',
            'description' => 'Test Description',
            'start_date' => now()->addDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(3)->format('Y-m-d'),
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'status' => 'active',
        ];

        $request = new \App\Http\Requests\API\V1\Camps\StoreCampRequest();
        $request->merge($validData);

        $validator = \Validator::make($validData, $request->rules());
        
        $this->assertFalse($validator->fails());
    }

    public function test_update_camp_with_valid_data(): void
    {
        $validData = [
            'name' => 'Updated Camp Name',
            'description' => 'Updated Description',
            'location' => 'Updated Location',
            'max_capacity' => 100,
            'price' => 2000,
            'status' => 'active',
        ];

        $request = new \App\Http\Requests\API\V1\Camps\UpdateCampRequest();
        $request->merge($validData);

        $validator = \Validator::make($validData, $request->rules());
        
        $this->assertFalse($validator->fails());
    }
}
