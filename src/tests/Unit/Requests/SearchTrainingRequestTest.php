<?php

declare(strict_types=1);

namespace Tests\Unit\Requests;

use App\Const\Training as TrainingConst;
use App\Http\Requests\API\V1\Training\SearchTrainingRequest;
use Tests\Unit\UnitTestCase;

class SearchTrainingRequestTest extends UnitTestCase
{

    private SearchTrainingRequest $request;

    protected function setUp(): void
    {
        parent::setUp();
        $this->request = new SearchTrainingRequest();
    }

    public function test_validation_rules_are_correct()
    {
        $rules = $this->request->rules();

        $this->assertArrayHasKey('name', $rules);
        $this->assertEquals('sometimes|string', $rules['name']);

        $this->assertArrayHasKey('location', $rules);
        $this->assertEquals('sometimes|string', $rules['location']);

        $this->assertArrayHasKey('start_date', $rules);
        $this->assertEquals('sometimes|date', $rules['start_date']);

        $this->assertArrayHasKey('end_date', $rules);
        $this->assertEquals('sometimes|date', $rules['end_date']);

        $this->assertArrayHasKey('price_min', $rules);
        $this->assertEquals('sometimes|numeric|min:0', $rules['price_min']);

        $this->assertArrayHasKey('price_max', $rules);
        $this->assertEquals('sometimes|numeric|min:0|gte:price_min', $rules['price_max']);

        $this->assertArrayHasKey('status', $rules);
        $this->assertEquals('sometimes|in:' . implode(',', TrainingConst::getStatuses()), $rules['status']);

        $this->assertArrayHasKey('per_page', $rules);
        $this->assertEquals('sometimes|integer|min:1|max:100', $rules['per_page']);

        $this->assertArrayHasKey('sort_column', $rules);
        $this->assertEquals('sometimes|string|in:name,start_date,end_date,price,created_at', $rules['sort_column']);

        $this->assertArrayHasKey('sort_order', $rules);
        $this->assertEquals('sometimes|string|in:asc,desc', $rules['sort_order']);
    }

    public function test_validation_passes_with_valid_search_data()
    {
        $validData = [
            'name' => 'Test Training',
            'location' => 'Test Location',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'price_min' => 50,
            'price_max' => 200,
            'status' => TrainingConst::STATUS_ACTIVE,
            'per_page' => 20,
            'sort_column' => 'name',
            'sort_order' => 'asc',
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_passes_with_empty_data()
    {
        $emptyData = [];

        $validator = validator($emptyData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_passes_with_partial_data()
    {
        $partialData = [
            'name' => 'Test Training',
            'price_min' => 50,
        ];

        $validator = validator($partialData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_negative_price_min()
    {
        $invalidData = [
            'price_min' => -10,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price_min', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_negative_price_max()
    {
        $invalidData = [
            'price_max' => -10,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price_max', $validator->errors()->toArray());
    }

    public function test_validation_fails_when_price_max_less_than_price_min()
    {
        $invalidData = [
            'price_min' => 100,
            'price_max' => 50, // Less than price_min
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price_max', $validator->errors()->toArray());
    }

    public function test_validation_passes_when_price_max_equals_price_min()
    {
        $validData = [
            'price_min' => 100,
            'price_max' => 100, // Equal to price_min
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_invalid_status()
    {
        $invalidData = [
            'status' => 'invalid_status',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_status()
    {
        $validStatuses = TrainingConst::getStatuses();
        
        foreach ($validStatuses as $status) {
            $validData = ['status' => $status];
            $validator = validator($validData, $this->request->rules());
            $this->assertTrue($validator->passes(), "Status '{$status}' should be valid");
        }
    }

    public function test_validation_fails_with_per_page_less_than_one()
    {
        $invalidData = [
            'per_page' => 0,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('per_page', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_per_page_greater_than_100()
    {
        $invalidData = [
            'per_page' => 101,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('per_page', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_sort_column()
    {
        $invalidData = [
            'sort_column' => 'invalid_column',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('sort_column', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_sort_columns()
    {
        $validSortColumns = ['name', 'start_date', 'end_date', 'price', 'created_at'];
        
        foreach ($validSortColumns as $sortColumn) {
            $validData = ['sort_column' => $sortColumn];
            $validator = validator($validData, $this->request->rules());
            $this->assertTrue($validator->passes(), "Sort column '{$sortColumn}' should be valid");
        }
    }

    public function test_validation_fails_with_invalid_sort_order()
    {
        $invalidData = [
            'sort_order' => 'invalid_order',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('sort_order', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_sort_orders()
    {
        $validSortOrders = ['asc', 'desc'];
        
        foreach ($validSortOrders as $sortOrder) {
            $validData = ['sort_order' => $sortOrder];
            $validator = validator($validData, $this->request->rules());
            $this->assertTrue($validator->passes(), "Sort order '{$sortOrder}' should be valid");
        }
    }

    public function test_validation_passes_with_zero_price()
    {
        $validData = [
            'price_min' => 0,
            'price_max' => 0,
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }
}
