<?php

declare(strict_types=1);

namespace Tests\Unit\Requests;

use App\Http\Requests\API\V1\Training\CreateTrainingRequest;
use Tests\Unit\UnitTestCase;

class CreateTrainingRequestTest extends UnitTestCase
{

    private CreateTrainingRequest $request;

    protected function setUp(): void
    {
        parent::setUp();
        $this->request = new CreateTrainingRequest();
    }

    public function test_validation_rules_are_correct()
    {
        $rules = $this->request->rules();

        $this->assertArrayHasKey('name', $rules);
        $this->assertEquals('required|string|max:255', $rules['name']);

        $this->assertArrayHasKey('description', $rules);
        $this->assertEquals('required|string|max:500', $rules['description']);

        $this->assertArrayHasKey('start_date', $rules);
        $this->assertEquals('required|date|after:now', $rules['start_date']);

        $this->assertArrayHas<PERSON>ey('end_date', $rules);
        $this->assertEquals('required|date|after_or_equal:start_date', $rules['end_date']);

        $this->assertArrayHasKey('location', $rules);
        $this->assertEquals('required|string|max:255', $rules['location']);

        $this->assertArrayHasKey('price', $rules);
        $this->assertEquals('required|integer|min:0', $rules['price']);

        $this->assertArrayHasKey('max_capacity', $rules);
        $this->assertEquals('required|integer|min:1', $rules['max_capacity']);
    }

    public function test_validation_passes_with_valid_data()
    {
        $validData = [
            'name' => 'Test Training',
            'description' => 'This is a test training description that is within the 500 character limit.',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_missing_required_fields()
    {
        $invalidData = [
            'name' => 'Test Training',
            // Missing other required fields
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('location', $validator->errors()->toArray());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_description_over_500_characters()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => str_repeat('a', 501), // 501 characters
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_negative_price()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => -10,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_zero_max_capacity()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 0,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_end_date_before_start_date()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(14)->toDateString(),
            'end_date' => now()->addDays(7)->toDateString(), // Before start_date
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_end_date_equal_to_start_date()
    {
        $startDate = now()->addDays(7)->toDateString();
        $validData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => $startDate,
            'end_date' => $startDate, // Equal to start_date
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_start_date_in_past()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->subDays(1)->toDateString(), // Past date
            'end_date' => now()->addDays(7)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
    }

    // Additional Failure Cases

    public function test_validation_fails_with_empty_name()
    {
        $invalidData = [
            'name' => '',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_name_over_255_characters()
    {
        $invalidData = [
            'name' => str_repeat('a', 256), // 256 characters
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_description()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => '',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_location()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => '',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('location', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_location_over_255_characters()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => str_repeat('a', 256), // 256 characters
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('location', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_date_format()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => 'invalid-date',
            'end_date' => 'invalid-date',
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_non_integer_price()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 'not-a-number',
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_non_integer_max_capacity()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 'not-a-number',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_negative_max_capacity()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => -5,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_start_date_equal_to_now()
    {
        $invalidData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->toDateString(), // Equal to now
            'end_date' => now()->addDays(7)->toDateString(),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_all_fields_missing()
    {
        $invalidData = [];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        
        $errors = $validator->errors()->toArray();
        $this->assertArrayHasKey('name', $errors);
        $this->assertArrayHasKey('description', $errors);
        $this->assertArrayHasKey('start_date', $errors);
        $this->assertArrayHasKey('end_date', $errors);
        $this->assertArrayHasKey('location', $errors);
        $this->assertArrayHasKey('price', $errors);
        $this->assertArrayHasKey('max_capacity', $errors);
    }

    public function test_validation_fails_with_null_values()
    {
        $invalidData = [
            'name' => null,
            'description' => null,
            'start_date' => null,
            'end_date' => null,
            'location' => null,
            'price' => null,
            'max_capacity' => null,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        
        $errors = $validator->errors()->toArray();
        $this->assertArrayHasKey('name', $errors);
        $this->assertArrayHasKey('description', $errors);
        $this->assertArrayHasKey('start_date', $errors);
        $this->assertArrayHasKey('end_date', $errors);
        $this->assertArrayHasKey('location', $errors);
        $this->assertArrayHasKey('price', $errors);
        $this->assertArrayHasKey('max_capacity', $errors);
    }
}
