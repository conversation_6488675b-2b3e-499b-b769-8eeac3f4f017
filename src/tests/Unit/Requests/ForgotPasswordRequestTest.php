<?php

declare(strict_types=1);

namespace Tests\Unit\Requests;

use App\Http\Requests\Portal\Auth\ForgotPasswordRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Unit\UnitTestCase;

class ForgotPasswordRequestTest extends UnitTestCase
{
    use RefreshDatabase;

    public function test_validation_passes_with_valid_email(): void
    {
        $request = new ForgotPasswordRequest;
        $request->merge(['email' => '<EMAIL>']);

        $validator = validator($request->all(), $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_invalid_email(): void
    {
        $request = new ForgotPasswordRequest;
        $request->merge(['email' => 'invalid-email']);

        $validator = validator($request->all(), $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('email'));
    }

    public function test_validation_fails_without_email(): void
    {
        $request = new ForgotPasswordRequest;
        $request->merge([]);

        $validator = validator($request->all(), $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('email'));
    }

    public function test_validation_fails_with_empty_email(): void
    {
        $request = new ForgotPasswordRequest;
        $request->merge(['email' => '']);

        $validator = validator($request->all(), $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('email'));
    }

    public function test_validation_fails_with_non_string_email(): void
    {
        $request = new ForgotPasswordRequest;
        $request->merge(['email' => 123]);

        $validator = validator($request->all(), $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('email'));
    }
}
