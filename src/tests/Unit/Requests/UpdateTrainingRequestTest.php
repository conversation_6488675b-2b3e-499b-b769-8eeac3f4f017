<?php

declare(strict_types=1);

namespace Tests\Unit\Requests;

use App\Const\Training as TrainingConst;
use App\Http\Requests\API\V1\Training\UpdateTrainingRequest;
use Tests\Unit\UnitTestCase;

class UpdateTrainingRequestTest extends UnitTestCase
{
    private UpdateTrainingRequest $request;

    protected function setUp(): void
    {
        parent::setUp();
        $this->request = new UpdateTrainingRequest;
    }

    public function test_validation_rules_are_correct()
    {
        $rules = $this->request->rules();

        $this->assertArrayHasKey('name', $rules);
        $this->assertEquals('sometimes|string|max:255', $rules['name']);

        $this->assertArrayHasKey('description', $rules);
        $this->assertEquals('sometimes|string|max:500', $rules['description']);

        $this->assertArrayHasKey('start_date', $rules);
        $this->assertEquals('sometimes|date', $rules['start_date']);

        $this->assertArrayHasKey('end_date', $rules);
        $this->assertEquals('sometimes|date|after_or_equal:start_date', $rules['end_date']);

        $this->assertArrayHasKey('location', $rules);
        $this->assertEquals('sometimes|string|max:255', $rules['location']);

        $this->assertArrayHasKey('price', $rules);
        $this->assertEquals('sometimes|integer|min:0', $rules['price']);

        $this->assertArrayHasKey('status', $rules);
        $this->assertEquals('sometimes|in:' . implode(',', TrainingConst::getStatuses()), $rules['status']);

        $this->assertArrayHasKey('max_capacity', $rules);
        $this->assertEquals('sometimes|integer|min:1', $rules['max_capacity']);
    }

    public function test_validation_passes_with_valid_data()
    {
        $validData = [
            'name' => 'Updated Training',
            'description' => 'This is an updated training description.',
            'start_date' => now()->addDays(7)->toDateString(),
            'end_date' => now()->addDays(14)->toDateString(),
            'location' => 'Updated Location',
            'price' => 150,
            'status' => TrainingConst::STATUS_ACTIVE,
            'max_capacity' => 25,
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_passes_with_partial_data()
    {
        $partialData = [
            'name' => 'Updated Training',
            'price' => 150,
        ];

        $validator = validator($partialData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_description_over_500_characters()
    {
        $invalidData = [
            'description' => str_repeat('a', 501), // 501 characters
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('description', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_negative_price()
    {
        $invalidData = [
            'price' => -10,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_zero_max_capacity()
    {
        $invalidData = [
            'max_capacity' => 0,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_status()
    {
        $invalidData = [
            'status' => 'invalid_status',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_valid_status()
    {
        $validStatuses = TrainingConst::getStatuses();

        foreach ($validStatuses as $status) {
            $validData = ['status' => $status];
            $validator = validator($validData, $this->request->rules());
            $this->assertTrue($validator->passes(), "Status '{$status}' should be valid");
        }
    }

    public function test_validation_fails_with_end_date_before_start_date()
    {
        $invalidData = [
            'start_date' => now()->addDays(14)->toDateString(),
            'end_date' => now()->addDays(7)->toDateString(), // Before start_date
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_validation_passes_with_end_date_equal_to_start_date()
    {
        $startDate = now()->addDays(7)->toDateString();
        $validData = [
            'start_date' => $startDate,
            'end_date' => $startDate, // Equal to start_date
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_passes_with_zero_price()
    {
        $validData = [
            'price' => 0, // Free training
        ];

        $validator = validator($validData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    // Additional Failure Cases

    public function test_validation_fails_with_empty_name()
    {
        $invalidData = [
            'name' => '',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Empty string is allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_name_over_255_characters()
    {
        $invalidData = [
            'name' => str_repeat('a', 256), // 256 characters
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_empty_description()
    {
        $invalidData = [
            'description' => '',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Empty string is allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_empty_location()
    {
        $invalidData = [
            'location' => '',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Empty string is allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_location_over_255_characters()
    {
        $invalidData = [
            'location' => str_repeat('a', 256), // 256 characters
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('location', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_invalid_date_format()
    {
        $invalidData = [
            'start_date' => 'invalid-date',
            'end_date' => 'invalid-date',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('start_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('end_date', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_non_integer_price()
    {
        $invalidData = [
            'price' => 'not-a-number',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_non_integer_max_capacity()
    {
        $invalidData = [
            'max_capacity' => 'not-a-number',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_negative_max_capacity()
    {
        $invalidData = [
            'max_capacity' => -5,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_null_values()
    {
        $invalidData = [
            'name' => null,
            'description' => null,
            'start_date' => null,
            'end_date' => null,
            'location' => null,
            'price' => null,
            'max_capacity' => null,
            'status' => null,
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());

        $errors = $validator->errors()->toArray();
        $this->assertArrayHasKey('name', $errors);
        $this->assertArrayHasKey('description', $errors);
        $this->assertArrayHasKey('start_date', $errors);
        $this->assertArrayHasKey('end_date', $errors);
        $this->assertArrayHasKey('location', $errors);
        $this->assertArrayHasKey('price', $errors);
        $this->assertArrayHasKey('max_capacity', $errors);
        $this->assertArrayHasKey('status', $errors);
    }

    public function test_validation_passes_with_empty_data()
    {
        $emptyData = [];

        $validator = validator($emptyData, $this->request->rules());
        $this->assertTrue($validator->passes());
    }

    public function test_validation_fails_with_start_date_in_past()
    {
        $invalidData = [
            'start_date' => now()->subDays(1)->toDateString(), // Past date
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Past date is allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_end_date_in_past()
    {
        $invalidData = [
            'end_date' => now()->subDays(1)->toDateString(), // Past date
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Past date is allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_multiple_invalid_fields()
    {
        $invalidData = [
            'description' => str_repeat('a', 501),
            'price' => -100,
            'max_capacity' => 0,
            'status' => 'invalid_status',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());

        $errors = $validator->errors()->toArray();
        $this->assertArrayHasKey('description', $errors);
        $this->assertArrayHasKey('price', $errors);
        $this->assertArrayHasKey('max_capacity', $errors);
        $this->assertArrayHasKey('status', $errors);
    }

    public function test_validation_fails_with_whitespace_only_strings()
    {
        $invalidData = [
            'name' => '   ',
            'description' => '   ',
            'location' => '   ',
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->passes()); // Whitespace strings are allowed with 'sometimes' rule
    }

    public function test_validation_fails_with_float_price()
    {
        $invalidData = [
            'price' => 99.99, // Float instead of integer
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }

    public function test_validation_fails_with_float_max_capacity()
    {
        $invalidData = [
            'max_capacity' => 25.5, // Float instead of integer
        ];

        $validator = validator($invalidData, $this->request->rules());
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('max_capacity', $validator->errors()->toArray());
    }
}
