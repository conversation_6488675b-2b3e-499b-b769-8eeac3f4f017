<?php

namespace Tests\Unit\Services;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use App\Repositories\TrainingRepository;
use App\Services\TrainingService;
use Illuminate\Support\Facades\Auth;
use Mockery;
use Tests\Unit\UnitTestCase;

class TrainingStatusServiceTest extends UnitTestCase
{
    private TrainingService $trainingService;
    private TrainingRepository $trainingRepository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->trainingRepository = Mockery::mock(TrainingRepository::class);
        $this->trainingService = new TrainingService($this->trainingRepository);
        
        Auth::shouldReceive('id')->andReturn('test-admin-id');
    }

    public function test_update_training_status_throws_exception_when_training_not_found(): void
    {
        $this->trainingRepository->shouldReceive('find')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('training_not_found');

        $this->trainingService->updateTrainingStatus(999, TrainingConst::STATUS_CANCELLED);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
