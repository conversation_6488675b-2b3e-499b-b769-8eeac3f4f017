<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Const\Training as TrainingConst;
use App\Enums\AttendanceStatus;
use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationStatus;
use App\Models\Attendance;
use App\Models\Child;
use App\Models\Registration;
use App\Models\Training;
use App\Models\User;
use App\Services\AttendanceService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\UnitTestCase;

class AttendanceServiceTest extends UnitTestCase
{
    use RefreshDatabase;

    private AttendanceService $attendanceService;
    private User $admin;
    private Training $training;
    private User $participant;
    private Child $child;
    private Registration $registration;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->attendanceService = new AttendanceService();
        $this->admin = User::factory()->create(['status' => 'active']);
        $this->participant = User::factory()->create(['status' => 'active']);
        $this->child = Child::factory()->create(['guardian_user_id' => $this->participant->id]);
        
        $this->training = Training::factory()->create([
            'status' => TrainingConst::STATUS_ACTIVE,
            'start_date' => Carbon::today()->subDays(5),
            'end_date' => Carbon::today()->addDays(5),
        ]);

        $this->registration = Registration::factory()->create([
            'user_id' => $this->participant->id,
            'child_id' => $this->child->id,
            'type' => RegistrationSourceType::TRAINING,
            'source_id' => $this->training->id,
            'status' => RegistrationStatus::APPROVED,
        ]);
    }

    public function test_can_record_attendance_for_active_training(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $date);
        
        $this->assertTrue($result['can_record']);
        $this->assertEmpty($result['errors']);
    }

    public function test_cannot_record_attendance_for_cancelled_training(): void
    {
        $this->training->update(['status' => TrainingConst::STATUS_CANCELLED]);
        $date = Carbon::today()->format('Y-m-d');
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $date);
        
        $this->assertFalse($result['can_record']);
        $this->assertContains('Cannot record attendance for training with status: cancelled', $result['errors']);
    }

    public function test_cannot_record_attendance_for_completed_training(): void
    {
        $this->training->update(['status' => TrainingConst::STATUS_COMPLETED]);
        $date = Carbon::today()->format('Y-m-d');
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $date);
        
        $this->assertFalse($result['can_record']);
        $this->assertContains('Cannot record attendance for training with status: completed', $result['errors']);
    }

    public function test_cannot_record_attendance_for_deleted_training(): void
    {
        $this->training->update(['status' => TrainingConst::STATUS_DELETED]);
        $date = Carbon::today()->format('Y-m-d');
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $date);
        
        $this->assertFalse($result['can_record']);
        $this->assertContains('Cannot record attendance for training with status: deleted', $result['errors']);
    }

    public function test_cannot_record_attendance_for_date_outside_training_range(): void
    {
        $futureDate = Carbon::today()->addDays(10)->format('Y-m-d');
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $futureDate);
        
        $this->assertFalse($result['can_record']);
        $this->assertContains('Attendance can only be recorded within training date range', $result['errors']);
    }

    public function test_cannot_record_attendance_for_validated_session(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        Attendance::factory()->create([
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => AttendanceStatus::PRESENT,
            'is_validated' => true,
            'validated_by' => $this->admin->id,
            'validated_at' => now(),
        ]);
        
        $result = $this->attendanceService->canRecordAttendance($this->training, $date);
        
        $this->assertFalse($result['can_record']);
        $this->assertContains('Attendance for this date has already been validated and cannot be modified', $result['errors']);
    }

    public function test_get_default_date_returns_today_when_within_range(): void
    {
        $defaultDate = $this->attendanceService->getDefaultDate($this->training);
        
        $this->assertEquals(Carbon::today()->format('Y-m-d'), $defaultDate);
    }

    public function test_get_default_date_returns_training_start_when_today_outside_range(): void
    {
        $this->training->update([
            'start_date' => Carbon::today()->addDays(10),
            'end_date' => Carbon::today()->addDays(15),
        ]);
        
        $defaultDate = $this->attendanceService->getDefaultDate($this->training);
        
        $this->assertEquals(Carbon::today()->addDays(10)->format('Y-m-d'), $defaultDate);
    }

    public function test_get_training_participants_returns_approved_registrations(): void
    {
        $participants = $this->attendanceService->getTrainingParticipants($this->training);
        
        $this->assertCount(1, $participants);
        $this->assertEquals($this->registration->id, $participants->first()->id);
    }

    public function test_get_training_participants_excludes_pending_registrations(): void
    {
        Registration::factory()->create([
            'user_id' => User::factory()->create()->id,
            'type' => RegistrationSourceType::TRAINING,
            'source_id' => $this->training->id,
            'status' => RegistrationStatus::PENDING,
        ]);
        
        $participants = $this->attendanceService->getTrainingParticipants($this->training);
        
        $this->assertCount(1, $participants);
        $this->assertEquals($this->registration->id, $participants->first()->id);
    }

    public function test_get_attendance_data_returns_correct_structure(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = $this->attendanceService->getAttendanceData($this->training, $date);
        
        $this->assertCount(1, $attendanceData);
        $this->assertEquals($this->registration->id, $attendanceData[0]['registration_id']);
        $this->assertEquals($this->participant->id, $attendanceData[0]['user_id']);
        $this->assertNull($attendanceData[0]['is_present']);
        $this->assertFalse($attendanceData[0]['is_validated']);
    }

    public function test_get_attendance_data_includes_existing_attendance(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        Attendance::factory()->create([
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => AttendanceStatus::PRESENT,
            'notes' => 'On time',
            'is_validated' => false,
        ]);
        
        $attendanceData = $this->attendanceService->getAttendanceData($this->training, $date);
        
        $this->assertTrue($attendanceData[0]['is_present']);
        $this->assertEquals('On time', $attendanceData[0]['notes']);
    }

    public function test_is_attendance_complete_returns_false_when_no_attendance(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        $isComplete = $this->attendanceService->isAttendanceComplete($this->training->id, $date);
        
        $this->assertFalse($isComplete);
    }

    public function test_is_attendance_complete_returns_true_when_all_marked(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        
        Attendance::factory()->create([
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => AttendanceStatus::PRESENT,
        ]);
        
        $isComplete = $this->attendanceService->isAttendanceComplete($this->training->id, $date);
        
        $this->assertTrue($isComplete);
    }

    public function test_save_attendance_successfully_creates_and_validates(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = [
            $this->registration->id => [
                'is_present' => true,
                'notes' => 'On time',
            ]
        ];
        
        $success = $this->attendanceService->saveAttendance(
            $this->training->id,
            $date,
            $attendanceData,
            $this->admin
        );
        
        $this->assertTrue($success);
        
        $this->assertDatabaseHas('attendances', [
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
            'status_id' => 'present',
            'notes' => 'On time',
            'is_validated' => true,
            'validated_by' => $this->admin->id,
        ]);
    }

    public function test_save_attendance_returns_false_when_incomplete(): void
    {
        $date = Carbon::today()->format('Y-m-d');
        $attendanceData = []; // Empty data
        
        $success = $this->attendanceService->saveAttendance(
            $this->training->id,
            $date,
            $attendanceData,
            $this->admin
        );
        
        $this->assertFalse($success);
        
        $this->assertDatabaseMissing('attendances', [
            'registration_id' => $this->registration->id,
            'attendance_date' => $date,
        ]);
    }
}
