<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\User;
use App\Services\TrainingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Mockery;
use Tests\Unit\UnitTestCase;

class TrainingServiceTest extends UnitTestCase
{
    use RefreshDatabase;

    protected TrainingService $trainingService;

    protected $trainingRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->trainingRepository = Mockery::mock('App\Repositories\TrainingRepository');
        $this->trainingService = new TrainingService($this->trainingRepository);
    }

    public function test_search_trainings_returns_paginated_results()
    {
        $search = ['name' => 'PHP'];
        $perPage = 10;
        $sortColumn = 'name';
        $sortOrder = 'asc';

        $expectedResults = collect([
            new Training(['name' => 'PHP Training']),
            new Training(['name' => 'Advanced PHP Training']),
        ]);

        $this->trainingRepository
            ->shouldReceive('searchTrainings')
            ->with($search, $perPage, $sortColumn, $sortOrder)
            ->once()
            ->andReturn($expectedResults);

        $result = $this->trainingService->searchTrainings($search, $perPage, $sortColumn, $sortOrder);

        $this->assertSame($expectedResults, $result);
    }

    public function test_create_training_sets_default_status_to_active()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(14),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
        ];

        $expectedTraining = new Training(array_merge($trainingData, [
            'status' => TrainingConst::STATUS_ACTIVE,
        ]));
        $expectedTraining->id = 1;

        $this->trainingRepository
            ->shouldReceive('create')
            ->with(array_merge($trainingData, [
                'created_by' => $user->id,
                'updated_by' => $user->id,
                'status' => TrainingConst::STATUS_ACTIVE,
            ]))
            ->once()
            ->andReturn($expectedTraining);

        $result = $this->trainingService->createTraining($trainingData);

        $this->assertSame($expectedTraining, $result);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $result->status);
    }

    public function test_create_training_preserves_custom_status()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(14),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
            'status' => TrainingConst::STATUS_CANCELLED,
        ];

        $expectedTraining = new Training($trainingData);
        $expectedTraining->id = 1;

        $this->trainingRepository
            ->shouldReceive('create')
            ->with(array_merge($trainingData, [
                'created_by' => $user->id,
                'updated_by' => $user->id,
            ]))
            ->once()
            ->andReturn($expectedTraining);

        $result = $this->trainingService->createTraining($trainingData);

        $this->assertSame($expectedTraining, $result);
        $this->assertEquals(TrainingConst::STATUS_CANCELLED, $result->status);
    }

    public function test_update_training_sets_updated_by()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingId = 1;
        $updateData = [
            'name' => 'Updated Training',
            'description' => 'Updated Description',
        ];

        $expectedTraining = new Training($updateData);
        $expectedTraining->id = $trainingId;

        $this->trainingRepository
            ->shouldReceive('update')
            ->with(array_merge($updateData, ['updated_by' => $user->id]), $trainingId)
            ->once()
            ->andReturn($expectedTraining);

        $result = $this->trainingService->updateTraining($trainingId, $updateData);

        $this->assertSame($expectedTraining, $result);
    }

    public function test_get_training_returns_training_by_id()
    {
        $trainingId = 1;
        $expectedTraining = new Training;
        $expectedTraining->id = $trainingId;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn($expectedTraining);

        $result = $this->trainingService->getTraining($trainingId);

        $this->assertSame($expectedTraining, $result);
    }

    public function test_delete_training_successfully_deletes()
    {
        $trainingId = 1;
        $training = new Training;
        $training->id = $trainingId;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn($training);

        $this->trainingRepository
            ->shouldReceive('delete')
            ->with($trainingId)
            ->once()
            ->andReturn(true);

        $result = $this->trainingService->deleteTraining($trainingId);

        $this->assertTrue($result);
    }

    public function test_delete_training_throws_exception_when_not_found()
    {
        $trainingId = 999;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn(null);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('training_not_found');

        $this->trainingService->deleteTraining($trainingId);
    }

    public function test_get_all_trainings_returns_paginated_results()
    {
        $perPage = 15;
        $sortColumn = 'created_at';
        $sortOrder = 'desc';

        $expectedResults = collect([
            new Training(['name' => 'Training 1']),
            new Training(['name' => 'Training 2']),
        ]);

        $this->trainingRepository
            ->shouldReceive('all')
            ->with([], $perPage, $sortColumn, $sortOrder, [], ['creator', 'updater'])
            ->once()
            ->andReturn($expectedResults);

        $result = $this->trainingService->getAllTrainings($perPage, $sortColumn, $sortOrder);

        $this->assertSame($expectedResults, $result);
    }

    public function test_create_training_throws_exception_when_repository_fails()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
        ];

        $this->trainingRepository
            ->shouldReceive('create')
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->trainingService->createTraining($trainingData);
    }

    public function test_update_training_throws_exception_when_repository_fails()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingId = 1;
        $updateData = ['name' => 'Updated Training'];

        $this->trainingRepository
            ->shouldReceive('update')
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->trainingService->updateTraining($trainingId, $updateData);
    }

    public function test_get_training_returns_null_when_not_found()
    {
        $trainingId = 999;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn(null);

        $result = $this->trainingService->getTraining($trainingId);

        $this->assertNull($result);
    }

    public function test_delete_training_throws_exception_when_delete_operation_fails()
    {
        $trainingId = 1;
        $training = new Training;
        $training->id = $trainingId;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn($training);

        $this->trainingRepository
            ->shouldReceive('delete')
            ->with($trainingId)
            ->once()
            ->andThrow(new \Exception('Delete failed'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Delete failed');

        $this->trainingService->deleteTraining($trainingId);
    }

    public function test_search_trainings_throws_exception_when_repository_fails()
    {
        $search = ['name' => 'PHP'];

        $this->trainingRepository
            ->shouldReceive('searchTrainings')
            ->andThrow(new \Exception('Search failed'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Search failed');

        $this->trainingService->searchTrainings($search);
    }

    public function test_get_all_trainings_throws_exception_when_repository_fails()
    {
        $this->trainingRepository
            ->shouldReceive('all')
            ->andThrow(new \Exception('Get all failed'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Get all failed');

        $this->trainingService->getAllTrainings();
    }

    public function test_create_training_logs_error_when_repository_fails()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
        ];

        $this->trainingRepository
            ->shouldReceive('create')
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);

        try {
            $this->trainingService->createTraining($trainingData);
        } catch (\Exception $e) {
            $this->assertEquals('Database error', $e->getMessage());
            throw $e;
        }
    }

    public function test_update_training_logs_error_when_repository_fails()
    {
        $user = User::factory()->create();
        Auth::shouldReceive('id')->andReturn($user->id);

        $trainingId = 1;
        $updateData = ['name' => 'Updated Training'];

        $this->trainingRepository
            ->shouldReceive('update')
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);

        try {
            $this->trainingService->updateTraining($trainingId, $updateData);
        } catch (\Exception $e) {
            $this->assertEquals('Database error', $e->getMessage());
            throw $e;
        }
    }

    public function test_delete_training_logs_error_when_delete_fails()
    {
        $trainingId = 1;
        $training = new Training;
        $training->id = $trainingId;

        $this->trainingRepository
            ->shouldReceive('find')
            ->with($trainingId)
            ->once()
            ->andReturn($training);

        $this->trainingRepository
            ->shouldReceive('delete')
            ->with($trainingId)
            ->once()
            ->andThrow(new \Exception('Delete failed'));

        $this->expectException(\Exception::class);

        try {
            $this->trainingService->deleteTraining($trainingId);
        } catch (\Exception $e) {
            $this->assertEquals('Delete failed', $e->getMessage());
            throw $e;
        }
    }

    public function test_create_training_with_empty_data_throws_exception()
    {
        $this->expectException(\Exception::class);
        $this->trainingService->createTraining([]);
    }

    public function test_update_training_with_empty_data_throws_exception()
    {
        $this->expectException(\Exception::class);
        $this->trainingService->updateTraining(1, []);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
