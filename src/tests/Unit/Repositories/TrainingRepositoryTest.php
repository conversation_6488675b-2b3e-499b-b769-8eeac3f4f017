<?php

declare(strict_types=1);

namespace Tests\Unit\Repositories;

use App\Models\Training;
use App\Models\User;
use App\Repositories\TrainingRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Unit\UnitTestCase;

class TrainingRepositoryTest extends UnitTestCase
{
    use RefreshDatabase;

    protected TrainingRepository $trainingRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->trainingRepository = new TrainingRepository;

        // Create a test user for all tests
        $this->user = User::factory()->create();
    }

    public function test_can_create_training()
    {
        $trainingData = [
            'name' => 'Test Training',
            'description' => 'Test Description',
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(14),
            'location' => 'Test Location',
            'price' => 100,
            'max_capacity' => 20,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ];

        $training = $this->trainingRepository->create($trainingData);

        $this->assertInstanceOf(Training::class, $training);
        $this->assertEquals('Test Training', $training->name);
        $this->assertEquals($this->user->id, $training->created_by);
        $this->assertEquals($this->user->id, $training->updated_by);
    }

    public function test_can_find_training_by_id()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $foundTraining = $this->trainingRepository->find($training->id);

        $this->assertInstanceOf(Training::class, $foundTraining);
        $this->assertEquals($training->id, $foundTraining->id);
    }

    public function test_can_update_training()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $updateData = [
            'name' => 'Updated Training',
            'description' => 'Updated Description',
        ];

        $updatedTraining = $this->trainingRepository->update($updateData, $training->id);

        $this->assertInstanceOf(Training::class, $updatedTraining);
        $this->assertEquals('Updated Training', $updatedTraining->name);
        $this->assertEquals('Updated Description', $updatedTraining->description);
    }

    public function test_can_delete_training()
    {
        $training = Training::factory()->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $result = $this->trainingRepository->delete($training->id);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('trainings', ['id' => $training->id]);
    }

    public function test_can_search_trainings()
    {
        // Create trainings with different names
        Training::factory()->create([
            'name' => 'PHP Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Laravel Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'JavaScript Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings(['name' => 'PHP']);

        $this->assertCount(1, $results);
        $this->assertEquals('PHP Training', $results->first()->name);
    }

    public function test_can_get_all_trainings()
    {
        Training::factory()->count(3)->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->all();

        $this->assertCount(3, $results);
    }

    public function test_search_trainings_with_multiple_criteria()
    {
        Training::factory()->create([
            'name' => 'PHP Training',
            'location' => 'Paris',
            'price' => 100,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Laravel Training',
            'location' => 'Paris',
            'price' => 200,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings([
            'location' => 'Paris',
            'price_min' => 150,
        ]);

        $this->assertCount(1, $results);
        $this->assertEquals('Laravel Training', $results->first()->name);
    }

    public function test_search_trainings_with_status_filter()
    {
        Training::factory()->create([
            'name' => 'Active Training',
            'status' => 'active',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Cancelled Training',
            'status' => 'cancelled',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings(['status' => 'active']);

        $this->assertCount(1, $results);
        $this->assertEquals('Active Training', $results->first()->name);
    }

    public function test_search_trainings_with_date_range()
    {
        Training::factory()->create([
            'name' => 'Future Training',
            'start_date' => now()->addDays(30),
            'end_date' => now()->addDays(37),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Past Training',
            'start_date' => now()->subDays(30),
            'end_date' => now()->subDays(23),
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings([
            'start_date' => now()->addDays(20),
        ]);

        $this->assertCount(1, $results);
        $this->assertEquals('Future Training', $results->first()->name);
    }

    public function test_search_trainings_with_price_range()
    {
        Training::factory()->create([
            'name' => 'Cheap Training',
            'price' => 50,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Expensive Training',
            'price' => 500,
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings([
            'price_min' => 100,
            'price_max' => 600,
        ]);

        $this->assertCount(1, $results);
        $this->assertEquals('Expensive Training', $results->first()->name);
    }

    public function test_search_trainings_with_sorting()
    {
        Training::factory()->create([
            'name' => 'Zebra Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        Training::factory()->create([
            'name' => 'Alpha Training',
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings([], 10, 'name', 'asc');

        $this->assertEquals('Alpha Training', $results->first()->name);
    }

    public function test_search_trainings_with_pagination()
    {
        Training::factory()->count(15)->create([
            'created_by' => $this->user->id,
            'updated_by' => $this->user->id,
        ]);

        $results = $this->trainingRepository->searchTrainings([], 10);

        $this->assertCount(10, $results);
    }

    public function test_find_returns_null_for_nonexistent_training()
    {
        $result = $this->trainingRepository->find(999);

        $this->assertNull($result);
    }

    public function test_delete_throws_exception_for_nonexistent_training()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->trainingRepository->delete(999);
    }

    public function test_update_throws_exception_for_nonexistent_training()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->trainingRepository->update(['name' => 'Updated'], 999);
    }
}
