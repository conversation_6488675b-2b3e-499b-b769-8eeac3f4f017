<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Repositories\TrainingRepository;
use App\Services\TrainingService;
use Tests\Unit\UnitTestCase;

class TrainingDuplicateTest extends UnitTestCase
{
    protected TrainingRepository $trainingRepository;
    protected TrainingService $trainingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->trainingRepository = app(TrainingRepository::class);
        $this->trainingService = app(TrainingService::class);
    }

    public function test_duplicate_training_creates_new_training_with_same_data(): void
    {
        $training = Training::factory()->withUser()->create([
            'name' => 'Original Training',
            'description' => 'Original Description',
            'location' => 'Test Location',
            'max_capacity' => 50,
            'price' => 1000,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertNotEquals($training->id, $duplicate->id);
        $this->assertEquals($training->name, $duplicate->name);
        $this->assertEquals($training->description, $duplicate->description);
        $this->assertEquals($training->location, $duplicate->location);
        $this->assertEquals($training->max_capacity, $duplicate->max_capacity);
        $this->assertEquals($training->price, $duplicate->price);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicate->status);
        $this->assertEquals($duplicateData['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($duplicateData['end_date'], $duplicate->end_date->format('Y-m-d'));
    }

    public function test_duplicate_training_requires_dates_and_user_fields(): void
    {
        $training = Training::factory()->withUser()->create([
            'name' => 'Test Training',
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertEquals($duplicateData['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($duplicateData['end_date'], $duplicate->end_date->format('Y-m-d'));
        $this->assertEquals($training->name, $duplicate->name);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicate->status);
    }

    public function test_duplicate_training_does_not_duplicate_id(): void
    {
        $training = Training::factory()->withUser()->create();

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertNotEquals($training->id, $duplicate->id);
        $this->assertNotNull($duplicate->id);
    }

    public function test_duplicate_training_does_not_duplicate_timestamps(): void
    {
        $training = Training::factory()->withUser()->create();

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        // Add a small delay to ensure different timestamps
        sleep(1);

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertNotEquals($training->created_at, $duplicate->created_at);
        $this->assertNotEquals($training->updated_at, $duplicate->updated_at);
    }

    public function test_duplicate_training_sets_status_to_active(): void
    {
        $training = Training::factory()->withUser()->create([
            'status' => TrainingConst::STATUS_CANCELLED,
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicate->status);
    }

    public function test_duplicate_training_preserves_all_fields_except_system_fields(): void
    {
        $training = Training::factory()->withUser()->create([
            'name' => 'Test Training',
            'description' => 'Test Description',
            'location' => 'Test Location',
            'max_capacity' => 75,
            'price' => 1500,
            'status' => TrainingConst::STATUS_CANCELLED,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertEquals($training->name, $duplicate->name);
        $this->assertEquals($training->description, $duplicate->description);
        $this->assertEquals($training->location, $duplicate->location);
        $this->assertEquals($training->max_capacity, $duplicate->max_capacity);
        $this->assertEquals($training->price, $duplicate->price);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicate->status); // Status should be reset to active
    }

    public function test_duplicate_training_creates_unique_record(): void
    {
        $training = Training::factory()->withUser()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate1 = $this->trainingRepository->duplicate($training, $duplicateData);
        $duplicate2 = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertNotEquals($training->id, $duplicate1->id);
        $this->assertNotEquals($training->id, $duplicate2->id);
        $this->assertNotEquals($duplicate1->id, $duplicate2->id);
    }

    public function test_duplicate_training_with_both_dates(): void
    {
        $training = Training::factory()->withUser()->create([
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
            'created_by' => $training->created_by,
            'updated_by' => $training->updated_by,
        ];

        $duplicate = $this->trainingRepository->duplicate($training, $duplicateData);

        $this->assertEquals($duplicateData['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($duplicateData['end_date'], $duplicate->end_date->format('Y-m-d'));
    }

    public function test_duplicate_training_service_method(): void
    {
        $training = Training::factory()->withUser()->create([
            'name' => 'Service Test Training',
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(3),
        ]);

        $duplicateData = [
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(12)->format('Y-m-d'),
        ];

        $duplicate = $this->trainingService->duplicateTraining($duplicateData, $training);

        $this->assertNotEquals($training->id, $duplicate->id);
        $this->assertEquals($training->name, $duplicate->name);
        $this->assertEquals(TrainingConst::STATUS_ACTIVE, $duplicate->status);
        $this->assertEquals($duplicateData['start_date'], $duplicate->start_date->format('Y-m-d'));
        $this->assertEquals($duplicateData['end_date'], $duplicate->end_date->format('Y-m-d'));
    }
}
