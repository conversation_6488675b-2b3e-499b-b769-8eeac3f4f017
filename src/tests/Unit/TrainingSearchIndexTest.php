<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\Training;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Unit\UnitTestCase;

class TrainingSearchIndexTest extends UnitTestCase
{
    use RefreshDatabase;

    public function test_search_indexes_are_created(): void
    {
        $this->artisan('migrate');

        $indexes = $this->getTableIndexes('trainings');

        $this->assertContains('trainings_name_index', $indexes);
        $this->assertContains('trainings_location_index', $indexes);
        $this->assertContains('trainings_start_date_index', $indexes);
        $this->assertContains('trainings_end_date_index', $indexes);
        $this->assertContains('trainings_price_index', $indexes);
        $this->assertContains('trainings_status_index', $indexes);
        $this->assertContains('trainings_status_start_date_index', $indexes);
        $this->assertContains('trainings_status_price_index', $indexes);
    }

    public function test_name_search_performance(): void
    {
        // Create multiple trainings with different names
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('name', 'like', '%Training%')->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Should execute quickly with index
        $this->assertLessThan(0.1, $executionTime);
    }

    public function test_location_search_performance(): void
    {
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('location', 'like', '%Location%')->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime);
    }

    public function test_date_range_search_performance(): void
    {
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('start_date', '>=', now())
            ->where('end_date', '<=', now()->addDays(30))
            ->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime);
    }

    public function test_price_range_search_performance(): void
    {
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('price', '>=', 100)
            ->where('price', '<=', 500)
            ->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime);
    }

    public function test_status_filter_performance(): void
    {
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('status', 'active')->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime);
    }

    public function test_composite_index_performance(): void
    {
        Training::factory()->count(100)->create();

        $startTime = microtime(true);

        $results = Training::where('status', 'active')
            ->where('start_date', '>=', now())
            ->get();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime);
    }

    private function getTableIndexes(string $tableName): array
    {
        $indexes = [];
        $results = \DB::select("SHOW INDEX FROM {$tableName}");

        foreach ($results as $result) {
            $indexes[] = $result->Key_name;
        }

        return array_unique($indexes);
    }
}
