<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Attendance;

use App\Http\Requests\API\APIRequest;
use App\Enums\AttendanceStatus;
use Illuminate\Validation\Rule;

class RecordAttendanceRequest extends APIRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'date' => 'required|date|date_format:Y-m-d',
            'attendance_data' => 'required|array',
            'attendance_data.*.registration_id' => 'required|integer|exists:registrations,id',
            'attendance_data.*.is_present' => 'required|boolean',
            'attendance_data.*.notes' => 'nullable|string|max:500',
        ];
    }

    public function messages(): array
    {
        return [
            'date.required' => 'Date is required',
            'date.date' => 'Date must be a valid date',
            'date.date_format' => 'Date must be in Y-m-d format',
            'attendance_data.required' => 'Attendance data is required',
            'attendance_data.array' => 'Attendance data must be an array',
            'attendance_data.*.registration_id.required' => 'Registration ID is required',
            'attendance_data.*.registration_id.exists' => 'Registration not found',
            'attendance_data.*.is_present.required' => 'Attendance status is required',
            'attendance_data.*.is_present.boolean' => 'Attendance status must be true or false',
            'attendance_data.*.notes.max' => 'Notes cannot exceed 500 characters',
        ];
    }
}
