<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use App\Http\Requests\API\APIRequest;

class ValidateAttendanceRequest extends APIRequest
{
    public function rules(): array
    {
        return [
            'attendance_id' => 'required|integer|exists:training_attendances,id',
        ];
    }

    public function messages(): array
    {
        return [
            'attendance_id.required' => 'Attendance ID is required',
            'attendance_id.integer' => 'Attendance ID must be an integer',
            'attendance_id.exists' => 'Attendance record not found',
        ];
    }
} 