<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use App\Http\Requests\API\APIRequest;
use Illuminate\Validation\Rule;

class RecordAttendanceRequest extends APIRequest
{
    public function rules(): array
    {
        return [
            'date' => 'required|date|date_format:Y-m-d',
            'participant_attendance' => 'required|array',
            'participant_attendance.*' => 'required|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'date.required' => 'Date is required',
            'date.date' => 'Date must be a valid date',
            'date.date_format' => 'Date must be in Y-m-d format',
            'participant_attendance.required' => 'Participant attendance is required',
            'participant_attendance.array' => 'Participant attendance must be an array',
            'participant_attendance.*.required' => 'Each participant attendance status is required',
            'participant_attendance.*.boolean' => 'Each participant attendance status must be true or false',
        ];
    }
} 