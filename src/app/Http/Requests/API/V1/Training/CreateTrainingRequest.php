<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use App\Const\Training as TrainingConst;
use App\Http\Requests\API\APIRequest;
use App\Models\Training;

class CreateTrainingRequest extends APIRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after_or_equal:start_date',
            'location' => 'required|string|max:255',
            'price' => 'required|integer|min:0',
            'max_capacity' => 'required|integer|min:1',
        ];
    }
}
