<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use Illuminate\Foundation\Http\FormRequest;

class DuplicateTrainingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
        ];
    }

    public function messages(): array
    {
        return [
            'start_date.after_or_equal' => 'start_name_after_or_equal_today',
            'end_date.after_or_equal' => 'end_date_after_or_equal_start_date',
        ];
    }
}
