<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use App\Const\Training as TrainingConst;
use App\Http\Requests\API\APIRequest;
use App\Models\Training;

class UpdateTrainingRequest extends APIRequest
{
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:500',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'location' => 'sometimes|string|max:255',
            'price' => 'sometimes|integer|min:0',
            'status' => 'sometimes|in:' . implode(',', TrainingConst::getStatuses()),
            'max_capacity' => 'sometimes|integer|min:1',
        ];
    }
}
