<?php

declare(strict_types=1);

namespace App\Http\Requests\API\V1\Training;

use App\Const\Training as TrainingConst;
use App\Http\Requests\API\APIRequest;

class SearchTrainingRequest extends APIRequest
{
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string',
            'location' => 'sometimes|string',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date',
            'price_min' => 'sometimes|numeric|min:0',
            'price_max' => 'sometimes|numeric|min:0|gte:price_min',
            'status' => 'sometimes|in:' . implode(',', TrainingConst::getStatuses()),
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'sort_column' => 'sometimes|string|in:name,start_date,end_date,price,created_at',
            'sort_order' => 'sometimes|string|in:asc,desc',
        ];
    }
}
