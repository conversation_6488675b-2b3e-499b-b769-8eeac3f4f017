<?php

namespace App\Http\Requests\API\V1\Camps;

use App\Enums\CampStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCampRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $campId = $this->route('camp')?->id ?? $this->route('id');
        return [
            'name' => [
                'required',
                'string',
                'max:500',
            ],
            'description' => 'required|string|max:500',
            'internal_note' => 'nullable|string|max:1000',
            'location' => 'required|string|max:255',
            'max_capacity' => 'required|integer|min:1',
            'price' => 'required|integer|min:0',
            'status' => Rule::in(CampStatus::values()),
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'camp_name_required',
            'description.required' => 'camp_description_required',
            'location.required' => 'camp_location_required',
            'max_capacity.required' => 'camp_max_capacity_required',
            'price.required' => 'camp_price_required',
            'status.in' => 'camp_status_invalid',
            'max_capacity.integer' => 'camp_max_capacity_integer',
            'price.integer' => 'camp_price_integer',
            'max_capacity.min' => 'camp_max_capacity_min',
            'price.min' => 'camp_price_min',
        ];
    }
}
