<?php

declare(strict_types=1);

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\API\ApiBaseController;
use App\Http\Requests\API\V1\Attendance\RecordAttendanceRequest;
use App\Http\Resources\API\V1\AttendanceResource;
use App\Services\AttendanceService;
use App\Services\TrainingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class AttendanceController extends ApiBaseController
{
    public function __construct(
        protected AttendanceService $attendanceService,
        protected TrainingService $trainingService
    ) {
    }

    public function show(Request $request, int $trainingId): JsonResponse
    {
        $training = $this->trainingService->getTraining($trainingId);
        
        if (!$training) {
            return $this->sendError(
                'training_not_found',
                [],
                [],
                ResponseAlias::HTTP_NOT_FOUND
            );
        }

        $date = $request->input('date', $this->attendanceService->getDefaultDate($training));
        
        $accessCheck = $this->attendanceService->canRecordAttendance($training, $date);
        if (!$accessCheck['can_record']) {
            return $this->sendError(
                'attendance_access_denied',
                $accessCheck['errors'],
                [],
                ResponseAlias::HTTP_FORBIDDEN
            );
        }

        $attendanceData = $this->attendanceService->getAttendanceData($training, $date);
        $isComplete = $this->attendanceService->isAttendanceComplete($training->id, $date);

        return $this->sendResponse(
            'attendance_data_retrieved_successfully',
            [
                'training' => [
                    'id' => $training->id,
                    'name' => $training->name,
                    'start_date' => $training->start_date,
                    'end_date' => $training->end_date,
                    'status' => $training->status,
                ],
                'date' => $date,
                'attendance_data' => $attendanceData,
                'is_complete' => $isComplete,
                'can_validate' => $isComplete,
            ]
        );
    }

    public function store(RecordAttendanceRequest $request, int $trainingId): JsonResponse
    {
        $training = $this->trainingService->getTraining($trainingId);
        
        if (!$training) {
            return $this->sendError(
                'training_not_found',
                [],
                [],
                ResponseAlias::HTTP_NOT_FOUND
            );
        }

        $date = $request->input('date');
        
        $accessCheck = $this->attendanceService->canRecordAttendance($training, $date);
        if (!$accessCheck['can_record']) {
            return $this->sendError(
                'attendance_access_denied',
                $accessCheck['errors'],
                [],
                ResponseAlias::HTTP_FORBIDDEN
            );
        }

        $attendanceData = [];
        foreach ($request->input('attendance_data') as $data) {
            $attendanceData[$data['registration_id']] = [
                'is_present' => $data['is_present'],
                'notes' => $data['notes'] ?? null,
            ];
        }

        try {
            $success = $this->attendanceService->saveAttendance(
                $training->id,
                $date,
                $attendanceData,
                auth()->user()
            );

            if (!$success) {
                return $this->sendError(
                    'attendance_incomplete',
                    ['All participants must have attendance marked before validation'],
                    [],
                    ResponseAlias::HTTP_BAD_REQUEST
                );
            }

            return $this->sendResponse(
                'attendance_recorded_successfully',
                [
                    'training_id' => $training->id,
                    'date' => $date,
                    'validated_by' => auth()->user()->id,
                    'validated_at' => now(),
                ],
                [],
                ResponseAlias::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->sendError(
                'attendance_save_failed',
                [$e->getMessage()],
                [],
                ResponseAlias::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function checkStatus(Request $request, int $trainingId): JsonResponse
    {
        $training = $this->trainingService->getTraining($trainingId);
        
        if (!$training) {
            return $this->sendError(
                'training_not_found',
                [],
                [],
                ResponseAlias::HTTP_NOT_FOUND
            );
        }

        $date = $request->input('date', $this->attendanceService->getDefaultDate($training));
        
        $accessCheck = $this->attendanceService->canRecordAttendance($training, $date);
        $isComplete = $this->attendanceService->isAttendanceComplete($training->id, $date);

        return $this->sendResponse(
            'attendance_status_retrieved_successfully',
            [
                'can_record' => $accessCheck['can_record'],
                'errors' => $accessCheck['errors'],
                'is_complete' => $isComplete,
                'can_validate' => $isComplete && $accessCheck['can_record'],
            ]
        );
    }
}
