<?php

declare(strict_types=1);

namespace App\Http\Controllers\API\V1;

use App\Const\Common;
use App\Http\Controllers\API\ApiBaseController;
use App\Http\Requests\API\V1\Training\CreateTrainingRequest;
use App\Http\Requests\API\V1\Training\DuplicateTrainingRequest;
use App\Http\Requests\API\V1\Training\SearchTrainingRequest;
use App\Http\Requests\API\V1\Training\UpdateTrainingRequest;
use App\Http\Resources\API\V1\TrainingResource;
use App\Services\TrainingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class TrainingController extends ApiBaseController
{
    public function __construct(protected TrainingService $trainingService)
    {
    }

    public function index(SearchTrainingRequest $request)
    {
        $search = $request->only([
            'name', 'location', 'start_date', 'end_date',
            'price_min', 'price_max', 'status',
        ]);

        $perPage = (int) $request->input('per_page', Common::ITEM_PER_PAGE);
        $sortColumn = $request->input('sort_column', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');

        $trainings = $this->trainingService->searchTrainings(
            $search,
            $perPage,
            $sortColumn,
            $sortOrder
        );

        return $this->sendPaginatedResponse(
            'training_listed_successfully',
            $trainings
        );
    }

    public function store(CreateTrainingRequest $request)
    {
        $training = $this->trainingService->createTraining($request->validated());

        return $this->sendResponse(
            'training_created_successfully',
            new TrainingResource($training),
            [],
            ResponseAlias::HTTP_CREATED
        );
    }

    public function show(int $id)
    {
        $training = $this->trainingService->getTraining($id);

        if (!$training) {
            return $this->sendError(
                'training_not_found',
                [],
                [],
                ResponseAlias::HTTP_NOT_FOUND
            );
        }

        return $this->sendResponse(
            'training_retrieved_successfully',
            new TrainingResource($training)
        );
    }

    public function update(UpdateTrainingRequest $request, int $id)
    {
        $training = $this->trainingService->updateTraining($id, $request->validated());

        return $this->sendResponse(
            'training_updated_successfully',
            new TrainingResource($training)
        );
    }

    public function destroy(int $id)
    {
        try {
            $this->trainingService->deleteTraining($id);

            return $this->sendResponse(
                'training_deleted_successfully',
                []
            );
        } catch (\Exception $e) {
            Log::error('Training deletion failed', [
                'training_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            if ($e->getMessage() === 'training_not_found') {
                return $this->sendError(
                    'training_not_found',
                    [],
                    [],
                    ResponseAlias::HTTP_NOT_FOUND
                );
            }

            return $this->sendError(
                'training_delete_failed',
                [],
                [],
                ResponseAlias::HTTP_BAD_REQUEST
            );
        }
    }

    public function duplicate(int $id, DuplicateTrainingRequest $request): JsonResponse
    {
        $training = $this->trainingService->getTraining($id);

        if (!$training) {
            return $this->sendError('training_not_found', [], ResponseAlias::HTTP_NOT_FOUND);
        }

        $data = $request->validated();
        $newTraining = $this->trainingService->duplicateTraining($data, $training);

        return $this->sendResponse(
            'training_duplicated_successfully',
            new TrainingResource($newTraining),
            '',
            ResponseAlias::HTTP_CREATED
        );
    }

    public function updateStatus(int $id, string $status): JsonResponse
    {
        try {
            $training = $this->trainingService->updateTrainingStatus($id, $status);

            return $this->sendResponse(
                'training_status_updated_successfully',
                new TrainingResource($training)
            );
        } catch (\Exception $e) {
            Log::error('Training status update failed', [
                'training_id' => $id,
                'status' => $status,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            if ($e->getMessage() === 'training_not_found') {
                return $this->sendError(
                    'training_not_found',
                    [],
                    [],
                    ResponseAlias::HTTP_NOT_FOUND
                );
            }

            return $this->sendError(
                'training_status_update_failed',
                [],
                [],
                ResponseAlias::HTTP_BAD_REQUEST
            );
        }
    }
}
