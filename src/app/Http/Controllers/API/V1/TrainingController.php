<?php

declare(strict_types=1);

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\API\ApiBaseController;
use App\Http\Requests\API\V1\Training\CreateTrainingRequest;
use App\Http\Requests\API\V1\Training\SearchTrainingRequest;
use App\Http\Requests\API\V1\Training\UpdateTrainingRequest;
use App\Http\Resources\API\V1\TrainingResource;
use App\Services\TrainingService;
use Illuminate\Support\Facades\Log;

class TrainingController extends ApiBaseController
{
    const int PER_PAGE = 20;

    public function __construct(protected TrainingService $trainingService)
    {
    }

    public function index(SearchTrainingRequest $request)
    {
        $search = $request->only([
            'name', 'location', 'start_date', 'end_date',
            'price_min', 'price_max', 'status',
        ]);

        $perPage = $request->input('per_page', self::PER_PAGE);
        $sortColumn = $request->input('sort_column', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');

        $trainings = $this->trainingService->searchTrainings(
            $search,
            $perPage,
            $sortColumn,
            $sortOrder
        );

        return $this->sendPaginatedResponse(
            'training_listed_successfully',
            $trainings
        );
    }

    public function store(CreateTrainingRequest $request)
    {
        $training = $this->trainingService->createTraining($request->validated());

        return $this->sendResponse(
            'training_created_successfully',
            new TrainingResource($training),
            [],
            201
        );
    }

    public function show(int $id)
    {
        $training = $this->trainingService->getTraining($id);

        if (!$training) {
            return $this->sendError(
                'training_not_found',
                [],
                [],
                404
            );
        }

        return $this->sendResponse(
            'training_retrieved_successfully',
            new TrainingResource($training)
        );
    }

    public function update(UpdateTrainingRequest $request, int $id)
    {
        $training = $this->trainingService->updateTraining($id, $request->validated());

        return $this->sendResponse(
            'training_updated_successfully',
            new TrainingResource($training)
        );
    }

    public function destroy(int $id)
    {
        try {
            $this->trainingService->deleteTraining($id);

            return $this->sendResponse(
                'training_deleted_successfully',
                []
            );
        } catch (\Exception $e) {
            Log::error('Training deletion failed', [
                'training_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            if ($e->getMessage() === 'training_not_found') {
                return $this->sendError(
                    'training_not_found',
                    [],
                    [],
                    404
                );
            }

            return $this->sendError(
                'training_delete_failed',
                [],
                [],
                400
            );
        }
    }
}
