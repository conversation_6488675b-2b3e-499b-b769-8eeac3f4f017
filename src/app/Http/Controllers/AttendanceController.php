<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\AttendanceService;
use App\Services\TrainingService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AttendanceController extends Controller
{
    public function __construct(
        protected AttendanceService $attendanceService,
        protected TrainingService $trainingService
    ) {
    }

    public function show(Request $request, int $trainingId): View
    {
        $training = $this->trainingService->getTraining($trainingId);
        
        if (!$training) {
            abort(404, 'Training not found');
        }

        $date = $request->input('date', $this->attendanceService->getDefaultDate($training));
        
        $accessCheck = $this->attendanceService->canRecordAttendance($training, $date);
        if (!$accessCheck['can_record']) {
            return view('attendance.access-denied', [
                'training' => $training,
                'errors' => $accessCheck['errors'],
            ]);
        }

        $attendanceData = $this->attendanceService->getAttendanceData($training, $date);
        $isComplete = $this->attendanceService->isAttendanceComplete($training->id, $date);

        return view('attendance.show', [
            'training' => $training,
            'date' => $date,
            'attendanceData' => $attendanceData,
            'isComplete' => $isComplete,
            'canValidate' => $isComplete,
        ]);
    }

    public function store(Request $request, int $trainingId)
    {
        $training = $this->trainingService->getTraining($trainingId);
        
        if (!$training) {
            abort(404, 'Training not found');
        }

        $date = $request->input('date');
        
        $accessCheck = $this->attendanceService->canRecordAttendance($training, $date);
        if (!$accessCheck['can_record']) {
            return redirect()->back()->withErrors($accessCheck['errors']);
        }

        $attendanceData = [];
        foreach ($request->input('attendance', []) as $registrationId => $data) {
            if (isset($data['status_id'])) {
                $attendanceData[$registrationId] = [
                    'status_id' => $data['status_id'],
                    'notes' => $data['notes'] ?? null,
                ];
            }
        }

        try {
            $success = $this->attendanceService->saveAttendance(
                $training->id,
                $date,
                $attendanceData,
                auth()->user()
            );

            if (!$success) {
                return redirect()->back()->withErrors(['All participants must have attendance marked before validation']);
            }

            return redirect()->route('trainings.show', $training->id)
                ->with('success', 'Attendance recorded and validated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['Failed to save attendance: ' . $e->getMessage()]);
        }
    }
}
