<?php

declare(strict_types=1);

namespace App\Http\Resources\API\V1;

use App\Http\Resources\API\ApiResource;
use Illuminate\Http\Request;

class TrainingAttendanceResource extends ApiResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'training_id' => $this->training_id,
            'attendance_date' => $this->attendance_date->format('Y-m-d'),
            'is_validated' => $this->is_validated,
            'validated_by' => $this->validated_by,
            'validated_at' => $this->validated_at?->format('Y-m-d H:i:s'),
            'participants' => $this->whenLoaded('attendanceRecords', function () {
                return $this->attendanceRecords->map(function ($record) {
                    $user = $record->trainingParticipant->user;
                    return [
                        'participant_id' => $record->trainingParticipant->id,
                        'user_id' => $user->id,
                        'name' => $user->first_name . ' ' . $user->last_name,
                        'age' => $user->date_of_birth ? $user->date_of_birth->age : null,
                        'parent_guardian' => $record->trainingParticipant->parent_guardian_name,
                        'health_notes' => $record->trainingParticipant->health_notes,
                        'is_present' => $record->is_present,
                    ];
                });
            }),
            'validator' => $this->whenLoaded('validator', function () {
                return [
                    'id' => $this->validator->id,
                    'name' => $this->validator->first_name . ' ' . $this->validator->last_name,
                ];
            }),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
} 