<?php

declare(strict_types=1);

namespace App\Http\Resources\API\V1;

use App\Enums\AttendanceStatus;
use App\Http\Resources\API\ApiResource;
use App\Models\Attendance;

class AttendanceResource extends ApiResource
{
    public function toArray($request): array
    {
        /** @var Attendance $attendance */
        $attendance = $this->resource;

        return [
            'attendance_id' => $attendance->attendance_id,
            'registration_id' => $attendance->registration_id,
            'attendance_date' => $attendance->attendance_date->format('Y-m-d'),
            'is_present' => $attendance->status_id === AttendanceStatus::PRESENT->value,
            'status_label' => $attendance->status_id?->label(),
            'notes' => $attendance->notes,
            'is_validated' => $attendance->is_validated,
            'validated_by' => $attendance->validated_by,
            'validated_at' => $attendance->validated_at?->format('Y-m-d H:i:s'),
            'created_at' => $attendance->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $attendance->updated_at->format('Y-m-d H:i:s'),
            'registration' => [
                'user' => [
                    'id' => $attendance->registration->user->id,
                    'name' => $attendance->registration->user->first_name . ' ' . $attendance->registration->user->last_name,
                ],
                'child' => $attendance->registration->child ? [
                    'id' => $attendance->registration->child->id,
                    'name' => $attendance->registration->child->first_name . ' ' . $attendance->registration->child->last_name,
                ] : null,
            ],
            'validated_by_user' => $attendance->validatedBy ? [
                'id' => $attendance->validatedBy->id,
                'name' => $attendance->validatedBy->first_name . ' ' . $attendance->validatedBy->last_name,
            ] : null,
        ];
    }
}
