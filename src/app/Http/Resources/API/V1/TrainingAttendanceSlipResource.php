<?php

declare(strict_types=1);

namespace App\Http\Resources\API\V1;

use App\Http\Resources\API\ApiResource;
use Illuminate\Http\Request;

class TrainingAttendanceSlipResource extends ApiResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'training_attendance_id' => $this->training_attendance_id,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'download_url' => route('api.v1.trainings.attendance.slips.download', [
                'training' => $this->trainingAttendance->training_id,
                'attendance' => $this->training_attendance_id,
                'slip' => $this->id,
            ]),
            'generated_by' => $this->generated_by,
            'generator' => $this->whenLoaded('generator', function () {
                return [
                    'id' => $this->generator->id,
                    'name' => $this->generator->first_name . ' ' . $this->generator->last_name,
                ];
            }),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
} 