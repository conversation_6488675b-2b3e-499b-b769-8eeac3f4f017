<?php

declare(strict_types=1);

namespace App\Const;

class Training
{
    public const STATUS_ACTIVE = 'active';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_FULL = 'full';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_DELETED = 'deleted';

    public static function getStatuses(): array
    {
        return [
            self::STATUS_ACTIVE,
            self::STATUS_CANCELLED,
            self::STATUS_FULL,
            self::STATUS_COMPLETED,
            self::STATUS_DELETED,
        ];
    }

    public static function getStatusLabel(string $status): string
    {
        return match ($status) {
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_FULL => 'Full',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_DELETED => 'Deleted',
            default => 'Unknown',
        };
    }

    public static function getStatusLabelFr(string $status): string
    {
        return match ($status) {
            self::STATUS_ACTIVE => 'Actif',
            self::STATUS_CANCELLED => 'Annulé',
            self::STATUS_FULL => 'Complet',
            self::STATUS_COMPLETED => 'Terminé',
            self::STATUS_DELETED => 'Supprimé',
            default => 'Inconnu',
        };
    }
}
