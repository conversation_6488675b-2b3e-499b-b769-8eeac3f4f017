<?php

namespace App\Console\Commands;

use App\Services\TrainingService;
use Illuminate\Console\Command;

class UpdateTrainingStatuses extends Command
{
    protected $signature = 'training:update-statuses';

    protected $description = 'Update training statuses based on capacity and end dates';

    public function __construct(protected TrainingService $trainingService)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->info('Starting training status update...');

        try {
            $this->trainingService->checkAndUpdateTrainingStatuses();
            
            $this->info('Training statuses updated successfully!');
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to update training statuses: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
