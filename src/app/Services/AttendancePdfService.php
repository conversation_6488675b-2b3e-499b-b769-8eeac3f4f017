<?php

declare(strict_types=1);

namespace App\Services;

use App\Const\File as FileType;
use App\Models\Attendance;
use App\Models\File;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AttendancePdfService
{
    public function __construct(
        protected FileService $fileService
    ) {
    }

    /**
     * Generate and store PDF for an attendance record
     */
    public function generateAttendancePdf(Attendance $attendance, User $generatedBy): File
    {
        Log::info('Starting PDF generation for attendance', [
            'attendance_id' => $attendance->attendance_id,
            'generated_by' => $generatedBy->id,
        ]);

        // Get participant data
        $participantData = $this->getParticipantData($attendance);
        
        // Generate PDF content
        $pdf = $this->createPdf($participantData, $attendance);
        
        // Save PDF to temporary file
        $tempFile = $this->savePdfToTempFile($pdf, $participantData);
        
        // Store file using FileService
        $file = $this->storeFileRecord($tempFile, $attendance, $generatedBy);
        
        Log::info('PDF generated successfully', [
            'attendance_id' => $attendance->attendance_id,
            'file_id' => $file->id,
            'file_path' => $file->path,
        ]);

        return $file;
    }

    /**
     * Get participant data for PDF generation
     */
    protected function getParticipantData(Attendance $attendance): array
    {
        $registration = $attendance->registration;
        $child = $registration->child;
        $guardian = $registration->user;
        $training = $this->getTrainingFromRegistration($registration);

        // Calculate age from date of birth
        $age = null;
        if ($child && $child->date_of_birth) {
            $age = Carbon::parse($child->date_of_birth)->age;
        } elseif ($guardian && $guardian->date_of_birth) {
            $age = Carbon::parse($guardian->date_of_birth)->age;
        }

        // Get name
        $name = $child ? $child->full_name : ($guardian->first_name . ' ' . $guardian->last_name);

        // Get parent/guardian info
        $parentGuardian = $child ? $child->emergency_contact_name : 'Self';

        // Get health notes
        $healthNotes = [];
        if ($child) {
            if ($child->allergies) {
                $healthNotes[] = 'Allergies: ' . $child->allergies;
            }
            if ($child->medical_conditions) {
                $healthNotes[] = 'Medical Conditions: ' . $child->medical_conditions;
            }
        }
        $healthNotesText = !empty($healthNotes) ? implode('; ', $healthNotes) : 'None';

        return [
            'name' => $name,
            'age' => $age,
            'parent_guardian' => $parentGuardian,
            'health_notes' => $healthNotesText,
            'attendance_date' => $attendance->attendance_date->format('Y-m-d'),
            'status' => $attendance->status_id->value,
            'training_name' => $training->name ?? 'Unknown Training',
            'notes' => $attendance->notes,
        ];
    }

    /**
     * Create PDF from participant data
     */
    protected function createPdf(array $participantData, Attendance $attendance): \Barryvdh\DomPDF\PDF
    {
        $data = [
            'participant' => $participantData,
            'attendance' => $attendance,
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];

        return Pdf::loadView('pdfs.attendance-slip', $data)
            ->setPaper('a4', 'portrait');
    }

    /**
     * Save PDF to temporary file
     */
    protected function savePdfToTempFile(\Barryvdh\DomPDF\PDF $pdf, array $participantData): UploadedFile
    {
        // Generate filename
        $filename = $this->generateFilename($participantData);
        
        // Create temp directory if it doesn't exist
        $tempDir = storage_path('app/temp/attendance-pdfs');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        // Save PDF to temp file
        $tempPath = $tempDir . '/' . $filename;
        file_put_contents($tempPath, $pdf->output());
        
        // Create UploadedFile instance
        return new UploadedFile(
            $tempPath,
            $filename,
            'application/pdf',
            null,
            true // test mode - don't validate file
        );
    }

    /**
     * Generate filename for PDF
     */
    protected function generateFilename(array $participantData): string
    {
        $safeName = Str::slug($participantData['name']);
        $date = $participantData['attendance_date'];
        $timestamp = now()->format('YmdHis');
        
        return "attendance-slip-{$safeName}-{$date}-{$timestamp}.pdf";
    }

    /**
     * Store file record using FileService
     */
    protected function storeFileRecord(UploadedFile $tempFile, Attendance $attendance, User $generatedBy): File
    {
        $file = $this->fileService->store([
            'file' => $tempFile,
            'type' => FileType::ATTENDANCE_PDF->value,
            'mime_type' => 'application/pdf',
        ], $attendance);

        // Update with generation info
        $file->update([
            'generated_by' => $generatedBy->id,
            'generated_at' => now(),
        ]);

        return $file;
    }

    /**
     * Get training from registration (handles polymorphic relationship)
     */
    protected function getTrainingFromRegistration($registration)
    {
        // Since registration has polymorphic relationship with source_id and type
        if ($registration->type === \App\Enums\RegistrationSourceType::TRAINING) {
            return \App\Models\Training::find($registration->source_id);
        }

        // For camps, we can still return some basic info
        if ($registration->type === \App\Enums\RegistrationSourceType::CAMP) {
            $camp = \App\Models\Camp::find($registration->source_id);
            return $camp ? (object)['name' => $camp->name] : null;
        }

        return null;
    }

    /**
     * Check if attendance already has a PDF generated
     */
    public function hasPdfGenerated(Attendance $attendance): bool
    {
        return $attendance->attendancePdfs()->exists();
    }

    /**
     * Get existing PDF for attendance
     */
    public function getExistingPdf(Attendance $attendance): ?File
    {
        return $attendance->attendancePdfs()->first();
    }
}
