<?php

declare(strict_types=1);

namespace App\Services;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Repositories\TrainingRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TrainingService
{
    public function __construct(protected TrainingRepository $trainingRepository)
    {
    }

    public function searchTrainings(array $search = [], int $perPage = 15, ?string $sortColumn = null, ?string $sortOrder = null)
    {
        return $this->trainingRepository->searchTrainings($search, $perPage, $sortColumn, $sortOrder);
    }

    public function createTraining(array $data): Training
    {
        return DB::transaction(function () use ($data) {
            $data['created_by'] = (string) Auth::id();
            $data['updated_by'] = (string) Auth::id();
            $data['status'] = $data['status'] ?? TrainingConst::STATUS_ACTIVE;

            try {
                $training = $this->trainingRepository->create($data);

                Log::info('Training created', [
                    'training_id' => $training->id,
                    'training_name' => $training->name,
                    'user_id' => Auth::id(),
                ]);

                return $training;
            } catch (\Exception $e) {
                Log::error('Failed to create training record', [
                    'error' => $e->getMessage(),
                    'training_data' => array_merge($data, ['password' => '[HIDDEN]']),
                    'user_id' => Auth::id(),
                ]);
                throw $e;
            }
        });
    }

    public function updateTraining(int $id, array $data): Training
    {
        return DB::transaction(function () use ($id, $data) {
            $data['updated_by'] = (string) Auth::id();

            try {
                $training = $this->trainingRepository->update($data, $id);

                Log::info('Training updated', [
                    'training_id' => $training->id,
                    'training_name' => $training->name,
                    'user_id' => Auth::id(),
                ]);

                return $training;
            } catch (\Exception $e) {
                Log::error('Failed to update training record', [
                    'error' => $e->getMessage(),
                    'training_id' => $id,
                    'update_data' => array_merge($data, ['password' => '[HIDDEN]']),
                    'user_id' => Auth::id(),
                ]);
                throw $e;
            }
        });
    }

    public function getTraining(int $id): ?Training
    {
        return $this->trainingRepository->find($id);
    }

    public function deleteTraining(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $training = $this->trainingRepository->find($id);

            if (!$training) {
                throw new \Exception('training_not_found');
            }

            try {
                $deleted = $this->trainingRepository->delete($id);

                if ($deleted) {
                    Log::info('Training deleted', [
                        'training_id' => $id,
                        'training_name' => $training->name,
                        'user_id' => Auth::id(),
                    ]);
                }

                return $deleted;
            } catch (\Exception $e) {
                Log::error('Failed to delete training record', [
                    'error' => $e->getMessage(),
                    'training_id' => $id,
                    'user_id' => Auth::id(),
                ]);
                throw $e;
            }
        });
    }

    public function getAllTrainings(int $perPage = 15, ?string $sortColumn = null, ?string $sortOrder = null)
    {
        return $this->trainingRepository->all([], $perPage, $sortColumn, $sortOrder, [], ['creator', 'updater']);
    }
}
