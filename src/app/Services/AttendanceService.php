<?php

declare(strict_types=1);

namespace App\Services;

use App\Const\Training as TrainingConst;
use App\Enums\AttendanceStatus;
use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationStatus;
use App\Models\Attendance;
use App\Models\Registration;
use App\Models\Training;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AttendanceService
{
    public function canRecordAttendance(Training $training, string $date): array
    {
        $errors = [];

        if ($training->isDeleted() || $training->isCancelled() || $training->isCompleted()) {
            $errors[] = 'Cannot record attendance for training with status: ' . $training->status;
        }

        $attendanceDate = Carbon::parse($date);
        $trainingStart = Carbon::parse($training->start_date);
        $trainingEnd = Carbon::parse($training->end_date);

        if ($attendanceDate->lt($trainingStart) || $attendanceDate->gt($trainingEnd)) {
            $errors[] = 'Attendance can only be recorded within training date range';
        }

        $existingAttendance = $this->getAttendanceForDate($training->id, $date);
        if ($existingAttendance->isNotEmpty() && $existingAttendance->first()->is_validated) {
            $errors[] = 'Attendance for this date has already been validated and cannot be modified';
        }

        return [
            'can_record' => empty($errors),
            'errors' => $errors
        ];
    }

    public function getDefaultDate(Training $training): string
    {
        $today = Carbon::today();
        $trainingStart = Carbon::parse($training->start_date);
        $trainingEnd = Carbon::parse($training->end_date);

        if ($today->between($trainingStart, $trainingEnd)) {
            return $today->format('Y-m-d');
        }

        return $trainingStart->format('Y-m-d');
    }

    public function getTrainingParticipants(Training $training): Collection
    {
        return Registration::where('type', RegistrationSourceType::TRAINING)
            ->where('source_id', $training->id)
            ->where('status', RegistrationStatus::APPROVED)
            ->with(['user', 'child'])
            ->get();
    }

    public function getAttendanceForDate(int $trainingId, string $date): Collection
    {
        return Attendance::whereHas('registration', function ($query) use ($trainingId) {
            $query->where('type', RegistrationSourceType::TRAINING)
                  ->where('source_id', $trainingId);
        })
        ->whereDate('attendance_date', $date)
        ->with(['registration.user', 'registration.child'])
        ->get();
    }

    public function getAttendanceData(Training $training, string $date): array
    {
        $participants = $this->getTrainingParticipants($training);
        $existingAttendance = $this->getAttendanceForDate($training->id, $date);

        $attendanceData = [];
        foreach ($participants as $participant) {
            $existingRecord = $existingAttendance->firstWhere('registration_id', $participant->id);
            
            $attendanceData[] = [
                'registration_id' => $participant->id,
                'user_id' => $participant->user_id,
                'user_name' => $participant->user->first_name . ' ' . $participant->user->last_name,
                'child_name' => $participant->child ? $participant->child->first_name . ' ' . $participant->child->last_name : null,
                'is_present' => $existingRecord ? ($existingRecord->status_id === AttendanceStatus::PRESENT->value) : null,
                'notes' => $existingRecord ? $existingRecord->notes : null,
                'is_validated' => $existingRecord ? $existingRecord->is_validated : false,
            ];
        }

        return $attendanceData;
    }

    public function saveAttendance(int $trainingId, string $date, array $attendanceData, User $validatedBy): bool
    {
        try {
            DB::beginTransaction();

            $participants = $this->getTrainingParticipants(Training::find($trainingId));
            $allMarked = true;

            foreach ($participants as $participant) {
                $attendanceRecord = $attendanceData[$participant->id] ?? null;
                
                if (!$attendanceRecord || !isset($attendanceRecord['is_present'])) {
                    $allMarked = false;
                    continue;
                }

                $statusId = $attendanceRecord['is_present'] ? AttendanceStatus::PRESENT->value : AttendanceStatus::ABSENT->value;

                $existingAttendance = Attendance::where('registration_id', $participant->id)
                    ->whereDate('attendance_date', $date)
                    ->first();

                if ($existingAttendance) {
                    $existingAttendance->update([
                        'status_id' => $statusId,
                        'notes' => $attendanceRecord['notes'] ?? null,
                    ]);
                } else {
                    Attendance::create([
                        'registration_id' => $participant->id,
                        'attendance_date' => $date,
                        'status_id' => $statusId,
                        'notes' => $attendanceRecord['notes'] ?? null,
                    ]);
                }
            }

            if (!$allMarked) {
                DB::rollBack();
                return false;
            }

            $this->validateAttendance($trainingId, $date, $validatedBy);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to save attendance', [
                'training_id' => $trainingId,
                'date' => $date,
                'error' => $e->getMessage(),
                'user_id' => $validatedBy->id,
            ]);
            throw $e;
        }
    }

    private function validateAttendance(int $trainingId, string $date, User $validatedBy): void
    {
        $attendances = $this->getAttendanceForDate($trainingId, $date);
        
        foreach ($attendances as $attendance) {
            $attendance->update([
                'is_validated' => true,
                'validated_by' => $validatedBy->id,
                'validated_at' => now(),
            ]);
        }
    }

    public function isAttendanceComplete(int $trainingId, string $date): bool
    {
        $participants = $this->getTrainingParticipants(Training::find($trainingId));
        $existingAttendance = $this->getAttendanceForDate($trainingId, $date);

        if ($participants->count() !== $existingAttendance->count()) {
            return false;
        }

        foreach ($existingAttendance as $attendance) {
            if ($attendance->status_id === null) {
                return false;
            }
        }

        return true;
    }
}
