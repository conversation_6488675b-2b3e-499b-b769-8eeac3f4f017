<?php

declare(strict_types=1);

namespace App\Services;

use App\Const\Training as TrainingConst;
use App\Models\Training;
use App\Models\TrainingAttendance;
use App\Repositories\TrainingAttendanceRepository;
use App\Repositories\TrainingAttendanceRecordRepository;
use App\Repositories\TrainingParticipantRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TrainingAttendanceService
{
    public function __construct(
        protected TrainingAttendanceRepository $attendanceRepository,
        protected TrainingAttendanceRecordRepository $attendanceRecordRepository,
        protected TrainingParticipantRepository $participantRepository
    ) {
    }

    public function canRecordAttendance(Training $training): bool
    {
        $today = Carbon::today();
        $startDate = Carbon::parse($training->start_date)->startOfDay();
        $endDate = Carbon::parse($training->end_date)->endOfDay();

        $invalidStatuses = [
            TrainingConst::STATUS_DELETED,
            TrainingConst::STATUS_CANCELLED,
            TrainingConst::STATUS_COMPLETED,
        ];

        return !in_array($training->status, $invalidStatuses) &&
               $today->between($startDate, $endDate);
    }

    public function getAttendanceData(int $trainingId, string $date): array
    {
        $training = Training::findOrFail($trainingId);
        
        if (!$this->canRecordAttendance($training)) {
            throw new \Exception('Cannot record attendance for this training');
        }

        $participants = $this->participantRepository->getParticipantsByTraining($trainingId);
        $attendance = $this->attendanceRepository->getAttendanceByTrainingAndDate($trainingId, $date);

        $attendanceData = [];
        foreach ($participants as $participant) {
            $isPresent = false;
            if ($attendance) {
                $record = $attendance->attendanceRecords
                    ->where('training_participant_id', $participant->id)
                    ->first();
                $isPresent = $record ? $record->is_present : false;
            }

            $attendanceData[] = [
                'participant_id' => $participant->id,
                'user_id' => $participant->user_id,
                'name' => $participant->user->first_name . ' ' . $participant->user->last_name,
                'age' => $participant->user->date_of_birth ? Carbon::parse($participant->user->date_of_birth)->age : null,
                'parent_guardian' => $participant->parent_guardian_name,
                'health_notes' => $participant->health_notes,
                'is_present' => $isPresent,
            ];
        }

        return [
            'training' => $training,
            'participants' => $attendanceData,
            'attendance' => $attendance,
            'can_validate' => $attendance && !$attendance->is_validated,
        ];
    }

    public function recordAttendance(int $trainingId, string $date, array $participantAttendance): TrainingAttendance
    {
        $training = Training::findOrFail($trainingId);
        
        if (!$this->canRecordAttendance($training)) {
            throw new \Exception('Cannot record attendance for this training');
        }

        return DB::transaction(function () use ($trainingId, $date, $participantAttendance) {
            $attendance = $this->attendanceRepository->createOrUpdateAttendance($trainingId, $date, []);
            
            $this->attendanceRecordRepository->updateAttendanceRecords(
                $attendance->id,
                $participantAttendance
            );

            Log::info('Attendance recorded', [
                'training_id' => $trainingId,
                'date' => $date,
                'user_id' => Auth::id(),
            ]);

            return $attendance->fresh(['attendanceRecords.trainingParticipant.user']);
        });
    }

    public function validateAttendance(int $trainingId, int $attendanceId): TrainingAttendance
    {
        $training = Training::findOrFail($trainingId);
        
        if (!$this->canRecordAttendance($training)) {
            throw new \Exception('Cannot validate attendance for this training');
        }

        $attendance = $this->attendanceRepository->find($attendanceId);
        
        if (!$attendance || $attendance->training_id !== $trainingId) {
            throw new \Exception('Attendance not found');
        }

        if ($attendance->is_validated) {
            throw new \Exception('Attendance already validated');
        }

        $validatedAttendance = $this->attendanceRepository->validateAttendance(
            $attendanceId,
            (string) Auth::id()
        );

        Log::info('Attendance validated', [
            'training_id' => $trainingId,
            'attendance_id' => $attendanceId,
            'user_id' => Auth::id(),
        ]);

        return $validatedAttendance;
    }

    public function getDefaultDate(Training $training): string
    {
        $today = Carbon::today();
        $startDate = Carbon::parse($training->start_date)->startOfDay();
        $endDate = Carbon::parse($training->end_date)->endOfDay();

        if ($today->between($startDate, $endDate)) {
            return $today->format('Y-m-d');
        }

        return $startDate->format('Y-m-d');
    }
} 