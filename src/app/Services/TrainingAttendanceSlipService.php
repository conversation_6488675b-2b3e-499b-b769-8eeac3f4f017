<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\TrainingAttendance;
use App\Models\TrainingAttendanceSlip;
use App\Repositories\TrainingAttendanceSlipRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class TrainingAttendanceSlipService
{
    public function __construct(
        protected TrainingAttendanceSlipRepository $slipRepository
    ) {
    }

    public function generateAttendanceSlip(TrainingAttendance $attendance): TrainingAttendanceSlip
    {
        if (!$attendance->is_validated) {
            throw new \Exception('Cannot generate slip for unvalidated attendance');
        }

        if ($attendance->attendanceSlip) {
            throw new \Exception('Attendance slip already generated');
        }

        $pdfContent = $this->generatePdfContent($attendance);
        $fileName = $this->generateFileName($attendance);
        $filePath = $this->savePdfFile($pdfContent, $fileName);

        $slip = $this->slipRepository->create([
            'training_attendance_id' => $attendance->id,
            'file_path' => $filePath,
            'file_name' => $fileName,
            'generated_by' => (string) Auth::id(),
        ]);

        return $slip;
    }

    private function generatePdfContent(TrainingAttendance $attendance): string
    {
        $training = $attendance->training;
        $participants = $attendance->attendanceRecords->map(function ($record) {
            $user = $record->trainingParticipant->user;
            $age = $user->date_of_birth ? Carbon::parse($user->date_of_birth)->age : 'N/A';
            
            return [
                'name' => $user->first_name . ' ' . $user->last_name,
                'age' => $age,
                'parent_guardian' => $record->trainingParticipant->parent_guardian_name ?? 'N/A',
                'health_notes' => $record->trainingParticipant->health_notes ?? 'N/A',
                'is_present' => $record->is_present ? 'Yes' : 'No',
            ];
        });

        $html = $this->generateHtmlContent($training, $attendance, $participants);
        
        return $this->convertHtmlToPdf($html);
    }

    private function generateHtmlContent($training, $attendance, $participants): string
    {
        $date = Carbon::parse($attendance->attendance_date)->format('F j, Y');
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <title>Attendance Slip - {$training->name}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                .training-info { margin-bottom: 20px; }
                .training-info table { width: 100%; border-collapse: collapse; }
                .training-info td { padding: 5px; }
                .participants-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                .participants-table th, .participants-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .participants-table th { background-color: #f2f2f2; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                .validated-info { margin-top: 20px; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Training Attendance Slip</h1>
                <h2>{$training->name}</h2>
            </div>
            
            <div class='training-info'>
                <table>
                    <tr><td><strong>Date:</strong></td><td>{$date}</td></tr>
                    <tr><td><strong>Location:</strong></td><td>{$training->location}</td></tr>
                    <tr><td><strong>Description:</strong></td><td>{$training->description}</td></tr>
                </table>
            </div>
            
            <table class='participants-table'>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Age</th>
                        <th>Parent/Guardian</th>
                        <th>Health Notes</th>
                        <th>Present</th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($participants as $participant) {
            $html .= "
                    <tr>
                        <td>{$participant['name']}</td>
                        <td>{$participant['age']}</td>
                        <td>{$participant['parent_guardian']}</td>
                        <td>{$participant['health_notes']}</td>
                        <td>{$participant['is_present']}</td>
                    </tr>";
        }
        
        $html .= "
                </tbody>
            </table>
            
            <div class='validated-info'>
                <p><strong>Validated by:</strong> {$attendance->validator->first_name} {$attendance->validator->last_name}</p>
                <p><strong>Validated at:</strong> {$attendance->validated_at->format('F j, Y g:i A')}</p>
            </div>
            
            <div class='footer'>
                <p>This attendance slip is validated and cannot be modified.</p>
                <p>Generated on: " . Carbon::now()->format('F j, Y g:i A') . "</p>
            </div>
        </body>
        </html>";
        
        return $html;
    }

    private function convertHtmlToPdf(string $html): string
    {
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        
        return $dompdf->output();
    }

    private function generateFileName(TrainingAttendance $attendance): string
    {
        $training = $attendance->training;
        $date = Carbon::parse($attendance->attendance_date)->format('Y-m-d');
        $trainingName = Str::slug($training->name);
        
        return "attendance_slip_{$trainingName}_{$date}.pdf";
    }

    private function savePdfFile(string $pdfContent, string $fileName): string
    {
        $directory = 'attendance-slips/' . date('Y/m');
        $filePath = $directory . '/' . $fileName;
        
        Storage::disk('local')->put($filePath, $pdfContent);
        
        return $filePath;
    }

    public function downloadSlip(TrainingAttendanceSlip $slip): string
    {
        if (!Storage::disk('local')->exists($slip->file_path)) {
            throw new \Exception('Slip file not found');
        }
        
        return Storage::disk('local')->get($slip->file_path);
    }
} 