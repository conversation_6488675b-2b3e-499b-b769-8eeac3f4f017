<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\TrainingParticipant;

class TrainingParticipantRepository extends BaseRepository
{
    protected $modelClass = TrainingParticipant::class;

    public function getParticipantsByTraining(int $trainingId)
    {
        return $this->model
            ->where('training_id', $trainingId)
            ->with(['user'])
            ->get();
    }

    public function addParticipant(int $trainingId, string $userId, array $data = []): TrainingParticipant
    {
        return $this->create([
            'training_id' => $trainingId,
            'user_id' => $userId,
            'parent_guardian_name' => $data['parent_guardian_name'] ?? null,
            'health_notes' => $data['health_notes'] ?? null,
        ]);
    }

    public function removeParticipant(int $trainingId, string $userId): bool
    {
        return $this->model
            ->where('training_id', $trainingId)
            ->where('user_id', $userId)
            ->delete();
    }
} 