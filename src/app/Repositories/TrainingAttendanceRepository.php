<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\TrainingAttendance;
use Carbon\Carbon;

class TrainingAttendanceRepository extends BaseRepository
{
    protected $modelClass = TrainingAttendance::class;

    public function getAttendanceByTrainingAndDate(int $trainingId, string $date): ?TrainingAttendance
    {
        return $this->model
            ->where('training_id', $trainingId)
            ->where('attendance_date', $date)
            ->with(['attendanceRecords.trainingParticipant.user'])
            ->first();
    }

    public function createOrUpdateAttendance(int $trainingId, string $date, array $attendanceData): TrainingAttendance
    {
        $attendance = $this->model
            ->where('training_id', $trainingId)
            ->where('attendance_date', $date)
            ->first();

        if (!$attendance) {
            $attendance = $this->create([
                'training_id' => $trainingId,
                'attendance_date' => $date,
                'is_validated' => false,
            ]);
        }

        return $attendance;
    }

    public function validateAttendance(int $attendanceId, string $validatedBy): TrainingAttendance
    {
        $attendance = $this->find($attendanceId);
        
        if ($attendance) {
            $attendance->update([
                'is_validated' => true,
                'validated_by' => $validatedBy,
                'validated_at' => Carbon::now(),
            ]);
        }

        return $attendance;
    }

    public function getAttendancesByTraining(int $trainingId)
    {
        return $this->model
            ->where('training_id', $trainingId)
            ->with(['attendanceRecords.trainingParticipant.user', 'validator'])
            ->orderBy('attendance_date', 'desc')
            ->get();
    }
} 