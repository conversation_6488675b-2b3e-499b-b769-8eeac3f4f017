<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\TrainingAttendanceSlip;

class TrainingAttendanceSlipRepository extends BaseRepository
{
    protected $modelClass = TrainingAttendanceSlip::class;

    public function getSlipByAttendanceId(int $attendanceId): ?TrainingAttendanceSlip
    {
        return $this->model
            ->where('training_attendance_id', $attendanceId)
            ->first();
    }
} 