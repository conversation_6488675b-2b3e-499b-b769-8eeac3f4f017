<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Const\Training as TrainingConst;
use App\Models\Training;

class TrainingRepository extends BaseRepository
{
    protected $modelClass = Training::class;

    public function searchTrainings(array $search = [], int $perPage = 15, ?string $sortColumn = null, ?string $sortOrder = null)
    {
        $query = $this->model->with(['creator', 'updater']);

        if (!empty($search['name'])) {
            $query->where('name', 'like', '%' . $search['name'] . '%');
        }

        if (!empty($search['location'])) {
            $query->where('location', 'like', '%' . $search['location'] . '%');
        }

        if (!empty($search['start_date'])) {
            $query->whereDate('start_date', '>=', $search['start_date']);
        }

        if (!empty($search['end_date'])) {
            $query->whereDate('end_date', '<=', $search['end_date']);
        }

        if (!empty($search['price_min'])) {
            $query->where('price', '>=', $search['price_min']);
        }

        if (!empty($search['price_max'])) {
            $query->where('price', '<=', $search['price_max']);
        }

        if (!empty($search['status'])) {
            $query->where('status', $search['status']);
        }

        if ($sortColumn && $sortOrder) {
            $query->orderBy($sortColumn, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query->paginate($perPage);
    }
}
