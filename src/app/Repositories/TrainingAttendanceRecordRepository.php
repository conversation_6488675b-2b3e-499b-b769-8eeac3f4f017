<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\TrainingAttendanceRecord;

class TrainingAttendanceRecordRepository extends BaseRepository
{
    protected $modelClass = TrainingAttendanceRecord::class;

    public function updateAttendanceRecords(int $attendanceId, array $participantAttendance): void
    {
        foreach ($participantAttendance as $participantId => $isPresent) {
            $this->model->updateOrCreate(
                [
                    'training_attendance_id' => $attendanceId,
                    'training_participant_id' => $participantId,
                ],
                [
                    'is_present' => $isPresent,
                ]
            );
        }
    }

    public function getAttendanceRecordsByAttendanceId(int $attendanceId)
    {
        return $this->model
            ->where('training_attendance_id', $attendanceId)
            ->with(['trainingParticipant.user'])
            ->get();
    }
} 