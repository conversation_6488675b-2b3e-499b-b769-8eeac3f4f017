<?php

namespace App\Enums;

enum RegistrationType: string
{
    case CHILD_REGISTRATION = 'child_registration';
    case TRAINEE_REGISTRATION = 'trainee_registration';
    case PARENT_REGISTRATION = 'parent_registration';

    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function labels(): array
    {
        return [
            self::CHILD_REGISTRATION->value => 'Child Registration',
            self::TRAINEE_REGISTRATION->value => 'Trainee Registration',
            self::PARENT_REGISTRATION->value => 'Parent Registration',
        ];
    }
}
