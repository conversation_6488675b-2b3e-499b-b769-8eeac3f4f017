<?php

namespace App\Enums;

enum RegistrationSourceType: string
{
    case CAMP = 'camp';
    case TRAINING = 'training';

    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function labels(): array
    {
        return [
            self::CAMP->value => 'Camp',
            self::TRAINING->value => 'Training',
        ];
    }
}
