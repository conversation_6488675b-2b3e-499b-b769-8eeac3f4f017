<?php

namespace App\Enums;

enum RegistrationStatus: string
{
    case PENDING_APPROVAL = 'pending_approval';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';
    case CONFIRMED = 'confirmed';
    case WAITING_LIST = 'waiting_list';
    case CANCELLED = 'cancelled';

    public static function values(): array
    {
        return array_map(fn ($case) => $case->value, self::cases());
    }

    public static function labels(): array
    {
        return [
            self::PENDING_APPROVAL->value => 'Pending Approval',
            self::APPROVED->value => 'Approved',
            self::REJECTED->value => 'Rejected',
            self::CONFIRMED->value => 'Confirmed',
            self::WAITING_LIST->value => 'Waiting List',
            self::CANCELLED->value => 'Cancelled',
        ];
    }
}
