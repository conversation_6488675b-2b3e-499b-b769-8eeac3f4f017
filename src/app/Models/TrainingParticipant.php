<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TrainingParticipant extends BaseModel
{
    protected $fillable = [
        'training_id',
        'user_id',
        'parent_guardian_name',
        'health_notes',
    ];

    protected $casts = [
        'training_id' => 'integer',
    ];

    public function training(): BelongsTo
    {
        return $this->belongsTo(Training::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(TrainingAttendanceRecord::class);
    }
} 