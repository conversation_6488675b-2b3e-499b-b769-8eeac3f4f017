<?php

namespace App\Models;

use App\Enums\RegistrationSourceType;
use App\Enums\RegistrationType;
use App\Enums\RegistrationStatus;
use App\Enums\PaymentStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Registration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'child_id',
        'type',
        'source_id',
        'registration_type',
        'status',
        'payment_status',
        'registered_at',
        'total_amount',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'type' => RegistrationSourceType::class,
        'registration_type' => RegistrationType::class,
        'status' => RegistrationStatus::class,
        'payment_status' => PaymentStatus::class,
        'registered_at' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function child(): BelongsTo
    {
        return $this->belongsTo(Child::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function camp(): BelongsTo
    {
        return $this->belongsTo(Camp::class, 'source_id');
    }

    public function scopeForCamp($query, $campId)
    {
        return $query->where('type', RegistrationSourceType::CAMP)
                    ->where('source_id', $campId);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', RegistrationStatus::APPROVED);
    }
}
