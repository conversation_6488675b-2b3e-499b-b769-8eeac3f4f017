<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class TrainingAttendance extends BaseModel
{
    protected $fillable = [
        'training_id',
        'attendance_date',
        'is_validated',
        'validated_by',
        'validated_at',
    ];

    protected $casts = [
        'training_id' => 'integer',
        'attendance_date' => 'date',
        'is_validated' => 'boolean',
        'validated_at' => 'datetime',
    ];

    public function training(): BelongsTo
    {
        return $this->belongsTo(Training::class);
    }

    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(TrainingAttendanceRecord::class);
    }

    public function attendanceSlip(): HasOne
    {
        return $this->hasOne(TrainingAttendanceSlip::class);
    }
} 