<?php

declare(strict_types=1);

namespace App\Models;

use App\Const\Training as TrainingConst;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Training extends BaseModel
{
    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'location',
        'price',
        'status',
        'max_capacity',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'price' => 'decimal:2',
        'max_capacity' => 'integer',
        'created_by' => 'string',
        'updated_by' => 'string',
    ];

    public function creator(): BelongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
