<?php

declare(strict_types=1);

namespace App\Models;

use App\Const\Training as TrainingConst;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Training extends BaseModel
{
    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'location',
        'price',
        'status',
        'max_capacity',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'price' => 'decimal:2',
        'max_capacity' => 'integer',
        'created_by' => 'string',
        'updated_by' => 'string',
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function isActive(): bool
    {
        return $this->status === TrainingConst::STATUS_ACTIVE;
    }

    public function isCancelled(): bool
    {
        return $this->status === TrainingConst::STATUS_CANCELLED;
    }

    public function isFull(): bool
    {
        return $this->status === TrainingConst::STATUS_FULL;
    }

    public function isCompleted(): bool
    {
        return $this->status === TrainingConst::STATUS_COMPLETED;
    }

    public function isDeleted(): bool
    {
        return $this->status === TrainingConst::STATUS_DELETED;
    }

    public function canAcceptRegistrations(): bool
    {
        return $this->isActive();
    }

    public function isPastEndDate(): bool
    {
        return now()->isAfter($this->end_date);
    }
}
