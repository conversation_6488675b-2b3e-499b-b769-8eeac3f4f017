<?php

namespace App\Models;

use App\Enums\AttendanceStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Attendance extends Model
{
    use HasFactory;

    protected $primaryKey = 'attendance_id';

    protected $fillable = [
        'registration_id',
        'attendance_date',
        'status_id',
        'notes',
        'is_validated',
        'validated_by',
        'validated_at',
    ];

    protected $casts = [
        'attendance_date' => 'datetime',
        'status_id' => AttendanceStatus::class,
        'is_validated' => 'boolean',
        'validated_at' => 'datetime',
    ];

    public function registration(): BelongsTo
    {
        return $this->belongsTo(Registration::class);
    }

    public function validatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    public function scopeForDate($query, $date)
    {
        return $query->whereDate('attendance_date', $date);
    }

    public function scopePresent($query)
    {
        return $query->where('status_id', AttendanceStatus::PRESENT);
    }

    public function scopeAbsent($query)
    {
        return $query->where('status_id', AttendanceStatus::ABSENT);
    }

    public function scopeValidated($query)
    {
        return $query->where('is_validated', true);
    }

    public function scopeNotValidated($query)
    {
        return $query->where('is_validated', false);
    }
}
