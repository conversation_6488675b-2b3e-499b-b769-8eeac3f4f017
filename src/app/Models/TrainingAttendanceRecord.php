<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TrainingAttendanceRecord extends BaseModel
{
    protected $fillable = [
        'training_attendance_id',
        'training_participant_id',
        'is_present',
    ];

    protected $casts = [
        'training_attendance_id' => 'integer',
        'training_participant_id' => 'integer',
        'is_present' => 'boolean',
    ];

    public function trainingAttendance(): BelongsTo
    {
        return $this->belongsTo(TrainingAttendance::class);
    }

    public function trainingParticipant(): BelongsTo
    {
        return $this->belongsTo(TrainingParticipant::class);
    }
} 