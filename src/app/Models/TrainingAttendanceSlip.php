<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TrainingAttendanceSlip extends BaseModel
{
    protected $fillable = [
        'training_attendance_id',
        'file_path',
        'file_name',
        'generated_by',
    ];

    protected $casts = [
        'training_attendance_id' => 'integer',
    ];

    public function trainingAttendance(): BelongsTo
    {
        return $this->belongsTo(TrainingAttendance::class);
    }

    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }
} 